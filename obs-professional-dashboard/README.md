# OBS Professional Dashboard

A complete, professional web-based dashboard for managing multiple OBS Studio instances with VLC playlist control across multiple computers.

## 🎯 Features

### ✅ **Multi-Instance Management**
- **Unlimited OBS instances** across multiple computers
- **Laptop/Computer grouping** for organized management
- **Real-time status monitoring** for all instances
- **Persistent connection management** with auto-reconnect

### ✅ **Professional Web Interface**
- **Responsive Bootstrap 5 design** works on desktop, tablet, mobile
- **Real-time updates** via WebSocket connections
- **Professional dark theme** optimized for streaming environments
- **Toast notifications** for all actions and status changes

### ✅ **Complete OBS Control**
- **Start/Stop streaming** for individual instances or bulk operations
- **Start/Stop recording** with real-time status indicators
- **Scene management** and switching
- **Source visibility** and control

### ✅ **VLC Playlist Management**
- **Complete VLC source control** - Play/Pause/Stop/Next/Previous
- **Real-time playback status** with progress bars and timings
- **Current file display** with duration information
- **Bulk VLC operations** across multiple instances
- **Individual source control** with detailed status

### ✅ **Bulk Operations**
- **Control all instances** simultaneously
- **Laptop-group operations** - control all instances on specific computers
- **VLC bulk control** - play/pause/stop all VLC sources at once
- **Streaming/Recording bulk control** across all connected instances

### ✅ **Real-time Monitoring**
- **Live connection status** for all instances
- **Streaming/Recording indicators** with visual feedback
- **Real-time statistics** in header dashboard
- **Automatic status updates** every 5 seconds

## 🚀 Quick Start

### 1. **Installation**
```bash
cd obs-professional-dashboard
npm install
npm start
```

### 2. **Access Dashboard**
Open your browser to: **http://localhost:3000**

### 3. **Add OBS Instances**
1. Click **"Add Instance"** button
2. Fill in the details:
   - **Name**: e.g., "Laptop-1-OBS-1"
   - **Host**: IP address (e.g., "**************")
   - **Port**: 4455 (default OBS WebSocket port)
   - **Password**: Leave empty if no password set
   - **Laptop**: Group name (e.g., "Laptop-1")

### 4. **OBS Studio Setup**
On each computer running OBS Studio:
1. Go to **Tools** → **obs-websocket Settings**
2. **Enable** "Enable WebSocket server"
3. Set **Port** to 4455 (or note custom port)
4. Set **Password** (optional, but recommended)
5. Click **OK**

## 📱 **Dashboard Interface**

### **Header Statistics**
- **Total Instances**: Number of added OBS instances
- **Connected**: Currently connected instances
- **Streaming**: Instances currently streaming
- **Recording**: Instances currently recording

### **Laptop Groups**
- **Organized by computer/laptop** for easy management
- **Group statistics** showing connected/streaming/recording counts
- **Bulk controls** for entire laptop groups
- **Individual instance cards** within each group

### **Instance Cards**
Each OBS instance shows:
- **Connection status** with colored indicators
- **Current streaming/recording status**
- **Current scene** information
- **VLC sources** with quick controls
- **Individual control buttons**

### **VLC Control Interface**
- **Detailed VLC control modal** for each instance
- **Real-time playback status** with progress bars
- **Current file information** with timing
- **Complete transport controls** (Play/Pause/Stop/Previous/Next)
- **Multiple VLC sources** supported per instance

## 🎛️ **Controls Available**

### **Individual Instance Controls**
- **Start/Stop Streaming** - Toggle streaming for specific instance
- **Start/Stop Recording** - Toggle recording for specific instance
- **VLC Controls** - Quick play/pause/stop for all VLC sources
- **Detailed VLC Control** - Open full VLC control interface
- **Refresh Instance** - Force status update
- **Remove Instance** - Delete from dashboard

### **Bulk Operations**
- **Start/Stop All Streaming** - Control streaming for all connected instances
- **Start/Stop All Recording** - Control recording for all connected instances
- **VLC Play/Pause/Stop All** - Control all VLC sources across all instances

### **Laptop Group Controls**
- **Group Streaming Control** - Start/stop streaming for all instances in laptop group
- **Group Statistics** - Real-time counts for the laptop group

## 🔧 **Configuration**

### **Environment Variables**
- `PORT` - Server port (default: 3000)

### **OBS WebSocket Settings**
- **Default Port**: 4455
- **Authentication**: Supported (optional)
- **Auto-reconnect**: Enabled with exponential backoff

## 📊 **Real-time Features**

### **WebSocket Updates**
- **Instant status changes** when streaming/recording starts/stops
- **Real-time connection monitoring** with automatic reconnection
- **Live VLC playback updates** with progress tracking
- **Toast notifications** for all actions and status changes

### **Auto-refresh**
- **Status updates** every 5 seconds for all connected instances
- **Connection health monitoring** with automatic reconnection attempts
- **Real-time statistics** in dashboard header

## 🎯 **Perfect for Your Use Case**

This dashboard is specifically designed for:
- **10 Windows 11 laptops** with **~7 OBS instances each**
- **60+ total OBS instances** managed from single web interface
- **VLC playlist control** within OBS instances
- **Professional streaming operations** with bulk control
- **Real-time monitoring** of all instances across your network

## 🔒 **Security**

- **Local network operation** - no external dependencies
- **OBS WebSocket authentication** supported
- **No data collection** - everything runs locally
- **Secure WebSocket connections** for real-time updates

## 🛠️ **Technical Details**

- **Backend**: Node.js with Express and Socket.IO
- **Frontend**: Bootstrap 5 with vanilla JavaScript
- **OBS Integration**: Official obs-websocket-js library
- **Real-time**: WebSocket connections for live updates
- **Responsive**: Works on desktop, tablet, and mobile devices

## 🎉 **Ready to Use!**

Your professional OBS dashboard is now running at **http://localhost:3000**

Add your OBS instances and start managing your entire streaming setup from one powerful web interface!
