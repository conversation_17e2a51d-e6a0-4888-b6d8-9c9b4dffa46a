{"version": 3, "sources": ["../src/types.ts", "../src/base.ts", "../src/utils/authenticationHashing.ts"], "sourcesContent": ["/**\n * This file is autogenerated by scripts/build-types.ts\n * To update this with latest changes do npm run generate:obs-types\n */\nimport type {Merge, JsonArray, JsonObject, JsonValue} from 'type-fest';\n\nexport enum WebSocketOpCode {\n/**\n * The initial message sent by obs-websocket to newly connected clients.\n *\n * Initial OBS Version: 5.0.0\n */\n\tHello = 0,\n\t/**\n\t * The message sent by a newly connected client to obs-websocket in response to a `Hello`.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tIdentify = 1,\n\t/**\n\t * The response sent by obs-websocket to a client after it has successfully identified with obs-websocket.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tIdentified = 2,\n\t/**\n\t * The message sent by an already-identified client to update identification parameters.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tReidentify = 3,\n\t/**\n\t * The message sent by obs-websocket containing an event payload.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tEvent = 5,\n\t/**\n\t * The message sent by a client to obs-websocket to perform a request.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tRequest = 6,\n\t/**\n\t * The message sent by obs-websocket in response to a particular request from a client.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tRequestResponse = 7,\n\t/**\n\t * The message sent by a client to obs-websocket to perform a batch of requests.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tRequestBatch = 8,\n\t/**\n\t * The message sent by obs-websocket in response to a particular batch of requests from a client.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tRequestBatchResponse = 9,\n}\n\n/* eslint-disable no-bitwise, @typescript-eslint/prefer-literal-enum-member */\nexport enum EventSubscription {\n/**\n * Subcription value used to disable all events.\n *\n * Initial OBS Version: 5.0.0\n */\n\tNone = 0,\n\t/**\n\t * Subscription value to receive events in the `General` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tGeneral = (1 << 0),\n\t/**\n\t * Subscription value to receive events in the `Config` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tConfig = (1 << 1),\n\t/**\n\t * Subscription value to receive events in the `Scenes` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tScenes = (1 << 2),\n\t/**\n\t * Subscription value to receive events in the `Inputs` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tInputs = (1 << 3),\n\t/**\n\t * Subscription value to receive events in the `Transitions` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tTransitions = (1 << 4),\n\t/**\n\t * Subscription value to receive events in the `Filters` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tFilters = (1 << 5),\n\t/**\n\t * Subscription value to receive events in the `Outputs` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tOutputs = (1 << 6),\n\t/**\n\t * Subscription value to receive events in the `SceneItems` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tSceneItems = (1 << 7),\n\t/**\n\t * Subscription value to receive events in the `MediaInputs` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tMediaInputs = (1 << 8),\n\t/**\n\t * Subscription value to receive the `VendorEvent` event.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tVendors = (1 << 9),\n\t/**\n\t * Subscription value to receive events in the `Ui` category.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tUi = (1 << 10),\n\t/**\n\t * Helper to receive all non-high-volume events.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tAll = (General | Config | Scenes | Inputs | Transitions | Filters | Outputs | SceneItems | MediaInputs | Vendors | Ui),\n\t/**\n\t * Subscription value to receive the `InputVolumeMeters` high-volume event.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tInputVolumeMeters = (1 << 16),\n\t/**\n\t * Subscription value to receive the `InputActiveStateChanged` high-volume event.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tInputActiveStateChanged = (1 << 17),\n\t/**\n\t * Subscription value to receive the `InputShowStateChanged` high-volume event.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tInputShowStateChanged = (1 << 18),\n\t/**\n\t * Subscription value to receive the `SceneItemTransformChanged` high-volume event.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tSceneItemTransformChanged = (1 << 19),\n}\n/* eslint-enable no-bitwise, @typescript-eslint/prefer-literal-enum-member */\n\nexport enum RequestBatchExecutionType {\n/**\n * Not a request batch.\n *\n * Initial OBS Version: 5.0.0\n */\n\tNone = -1,\n\t/**\n\t * A request batch which processes all requests serially, as fast as possible.\n\t *\n\t * Note: To introduce artificial delay, use the `Sleep` request and the `sleepMillis` request field.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tSerialRealtime = 0,\n\t/**\n\t * A request batch type which processes all requests serially, in sync with the graphics thread. Designed to provide high accuracy for animations.\n\t *\n\t * Note: To introduce artificial delay, use the `Sleep` request and the `sleepFrames` request field.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tSerialFrame = 1,\n\t/**\n\t * A request batch type which processes all requests using all available threads in the thread pool.\n\t *\n\t * Note: This is mainly experimental, and only really shows its colors during requests which require lots of\n\t * active processing, like `GetSourceScreenshot`.\n\t *\n\t * Initial OBS Version: 5.0.0\n\t */\n\tParallel = 2,\n}\n\n// WebSocket Message Types\nexport type IncomingMessage<Type = keyof IncomingMessageTypes> = Type extends keyof IncomingMessageTypes ? {\n\top: Type;\n\td: IncomingMessageTypes[Type];\n} : never;\n\nexport type OutgoingMessage<Type = keyof OutgoingMessageTypes> = Type extends keyof OutgoingMessageTypes ? {\n\top: Type;\n\td: OutgoingMessageTypes[Type];\n} : never;\n\nexport interface IncomingMessageTypes {\n\t/**\n\t * Message sent from the server immediately on client connection. Contains authentication information if auth is required. Also contains RPC version for version negotiation.\n\t */\n\t[WebSocketOpCode.Hello]: {\n\t\t/**\n\t\t * Version number of obs-websocket\n\t\t */\n\t\tobsWebSocketVersion: string;\n\t\t/**\n\t\t * Version number which gets incremented on each breaking change to the obs-websocket protocol.\n\t\t * It's usage in this context is to provide the current rpc version that the server would like to use.\n\t\t */\n\t\trpcVersion: number;\n\t\t/**\n\t\t * Authentication challenge when password is required\n\t\t */\n\t\tauthentication?: {\n\t\t\tchallenge: string;\n\t\t\tsalt: string;\n\t\t};\n\t};\n\t/**\n\t * The identify request was received and validated, and the connection is now ready for normal operation.\n\t */\n\t[WebSocketOpCode.Identified]: {\n\t\t/**\n\t\t * If rpc version negotiation succeeds, the server determines the RPC version to be used and gives it to the client\n\t\t */\n\t\tnegotiatedRpcVersion: number;\n\t};\n\t/**\n\t * An event coming from OBS has occured. Eg scene switched, source muted.\n\t */\n\t[WebSocketOpCode.Event]: EventMessage;\n\t/**\n\t * obs-websocket is responding to a request coming from a client\n\t */\n\t[WebSocketOpCode.RequestResponse]: ResponseMessage;\n\t/**\n\t * obs-websocket is responding to a batch request coming from a client\n\t */\n\t[WebSocketOpCode.RequestBatchResponse]: ResponseBatchMessage;\n}\n\nexport interface OutgoingMessageTypes {\n\t/**\n\t * Response to Hello message, should contain authentication string if authentication is required, along with PubSub subscriptions and other session parameters.\n\t */\n\t[WebSocketOpCode.Identify]: {\n\t\t/**\n\t\t * Version number that the client would like the obs-websocket server to use\n\t\t */\n\t\trpcVersion: number;\n\t\t/**\n\t\t * Authentication challenge response\n\t\t */\n\t\tauthentication?: string;\n\t\t/**\n\t\t * Bitmask of `EventSubscription` items to subscribe to events and event categories at will. By default, all event categories are subscribed, except for events marked as high volume. High volume events must be explicitly subscribed to.\n\t\t */\n\t\teventSubscriptions?: number;\n\t};\n\t/**\n\t * Sent at any time after initial identification to update the provided session parameters.\n\t */\n\t[WebSocketOpCode.Reidentify]: {\n\t\t/**\n\t\t * Bitmask of `EventSubscription` items to subscribe to events and event categories at will. By default, all event categories are subscribed, except for events marked as high volume. High volume events must be explicitly subscribed to.\n\t\t */\n\t\teventSubscriptions?: number;\n\t};\n\t/**\n\t * Client is making a request to obs-websocket. Eg get current scene, create source.\n\t */\n\t[WebSocketOpCode.Request]: RequestMessage;\n\t/**\n\t * Client is making a batch request to obs-websocket.\n\t */\n\t[WebSocketOpCode.RequestBatch]: RequestBatchMessage;\n}\n\ntype EventMessage<T = keyof OBSEventTypes> = T extends keyof OBSEventTypes ? {\n\teventType: T;\n\t/**\n\t * The original intent required to be subscribed to in order to receive the event.\n\t */\n\teventIntent: number;\n\teventData: OBSEventTypes[T];\n} : never;\n\nexport type RequestMessage<T = keyof OBSRequestTypes> = T extends keyof OBSRequestTypes ? {\n\trequestType: T;\n\trequestId: string;\n\trequestData: OBSRequestTypes[T];\n} : never;\n\nexport type RequestBatchRequest<T = keyof OBSRequestTypes> = T extends keyof OBSRequestTypes ? OBSRequestTypes[T] extends never ? {\n\trequestType: T;\n\trequestId?: string;\n} : {\n\trequestType: T;\n\trequestId?: string;\n\trequestData: OBSRequestTypes[T];\n} : never;\n\nexport type RequestBatchOptions = {\n\t/**\n\t * The mode of execution obs-websocket will run the batch in\n\t */\n\texecutionType?: RequestBatchExecutionType;\n\t/**\n\t * Whether obs-websocket should stop executing the batch if one request fails\n\t */\n\thaltOnFailure?: boolean;\n};\n\nexport type RequestBatchMessage = Merge<RequestBatchOptions, {\n\trequestId: string;\n\trequests: RequestBatchRequest[];\n}>;\n\nexport type ResponseMessage<T = keyof OBSResponseTypes> = T extends keyof OBSResponseTypes ? {\n\trequestType: T;\n\trequestId: string;\n\trequestStatus: {result: true; code: number} | {result: false; code: number; comment: string};\n\tresponseData: OBSResponseTypes[T];\n} : never;\n\nexport type ResponseBatchMessage = {\n\trequestId: string;\n\tresults: ResponseMessage[];\n};\n\n// Events\nexport interface OBSEventTypes {\n\tCurrentSceneCollectionChanging: {\n\t\t/**\n\t\t * Name of the current scene collection\n\t\t */\n\t\tsceneCollectionName: string;\n\t};\n\tCurrentSceneCollectionChanged: {\n\t\t/**\n\t\t * Name of the new scene collection\n\t\t */\n\t\tsceneCollectionName: string;\n\t};\n\tSceneCollectionListChanged: {\n\t\t/**\n\t\t * Updated list of scene collections\n\t\t */\n\t\tsceneCollections: string[];\n\t};\n\tCurrentProfileChanging: {\n\t\t/**\n\t\t * Name of the current profile\n\t\t */\n\t\tprofileName: string;\n\t};\n\tCurrentProfileChanged: {\n\t\t/**\n\t\t * Name of the new profile\n\t\t */\n\t\tprofileName: string;\n\t};\n\tProfileListChanged: {\n\t\t/**\n\t\t * Updated list of profiles\n\t\t */\n\t\tprofiles: string[];\n\t};\n\tSourceFilterListReindexed: {\n\t\t/**\n\t\t * Name of the source\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Array of filter objects\n\t\t */\n\t\tfilters: JsonObject[];\n\t};\n\tSourceFilterCreated: {\n\t\t/**\n\t\t * Name of the source the filter was added to\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * The kind of the filter\n\t\t */\n\t\tfilterKind: string;\n\t\t/**\n\t\t * Index position of the filter\n\t\t */\n\t\tfilterIndex: number;\n\t\t/**\n\t\t * The settings configured to the filter when it was created\n\t\t */\n\t\tfilterSettings: JsonObject;\n\t\t/**\n\t\t * The default settings for the filter\n\t\t */\n\t\tdefaultFilterSettings: JsonObject;\n\t};\n\tSourceFilterRemoved: {\n\t\t/**\n\t\t * Name of the source the filter was on\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t};\n\tSourceFilterNameChanged: {\n\t\t/**\n\t\t * The source the filter is on\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Old name of the filter\n\t\t */\n\t\toldFilterName: string;\n\t\t/**\n\t\t * New name of the filter\n\t\t */\n\t\tfilterName: string;\n\t};\n\tSourceFilterSettingsChanged: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * New settings object of the filter\n\t\t */\n\t\tfilterSettings: JsonObject;\n\t};\n\tSourceFilterEnableStateChanged: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * Whether the filter is enabled\n\t\t */\n\t\tfilterEnabled: boolean;\n\t};\n\tExitStarted: undefined;\n\tInputCreated: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * The kind of the input\n\t\t */\n\t\tinputKind: string;\n\t\t/**\n\t\t * The unversioned kind of input (aka no `_v2` stuff)\n\t\t */\n\t\tunversionedInputKind: string;\n\t\t/**\n\t\t * The settings configured to the input when it was created\n\t\t */\n\t\tinputSettings: JsonObject;\n\t\t/**\n\t\t * The default settings for the input\n\t\t */\n\t\tdefaultInputSettings: JsonObject;\n\t};\n\tInputRemoved: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t};\n\tInputNameChanged: {\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * Old name of the input\n\t\t */\n\t\toldInputName: string;\n\t\t/**\n\t\t * New name of the input\n\t\t */\n\t\tinputName: string;\n\t};\n\tInputSettingsChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * New settings object of the input\n\t\t */\n\t\tinputSettings: JsonObject;\n\t};\n\tInputActiveStateChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * Whether the input is active\n\t\t */\n\t\tvideoActive: boolean;\n\t};\n\tInputShowStateChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * Whether the input is showing\n\t\t */\n\t\tvideoShowing: boolean;\n\t};\n\tInputMuteStateChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * Whether the input is muted\n\t\t */\n\t\tinputMuted: boolean;\n\t};\n\tInputVolumeChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * New volume level multiplier\n\t\t */\n\t\tinputVolumeMul: number;\n\t\t/**\n\t\t * New volume level in dB\n\t\t */\n\t\tinputVolumeDb: number;\n\t};\n\tInputAudioBalanceChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * New audio balance value of the input\n\t\t */\n\t\tinputAudioBalance: number;\n\t};\n\tInputAudioSyncOffsetChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * New sync offset in milliseconds\n\t\t */\n\t\tinputAudioSyncOffset: number;\n\t};\n\tInputAudioTracksChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * Object of audio tracks along with their associated enable states\n\t\t */\n\t\tinputAudioTracks: JsonObject;\n\t};\n\tInputAudioMonitorTypeChanged: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * New monitor type of the input\n\t\t */\n\t\tmonitorType: string;\n\t};\n\tInputVolumeMeters: {\n\t\t/**\n\t\t * Array of active inputs with their associated volume levels\n\t\t */\n\t\tinputs: JsonObject[];\n\t};\n\tMediaInputPlaybackStarted: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t};\n\tMediaInputPlaybackEnded: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t};\n\tMediaInputActionTriggered: {\n\t\t/**\n\t\t * Name of the input\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * Action performed on the input. See `ObsMediaInputAction` enum\n\t\t */\n\t\tmediaAction: string;\n\t};\n\tStreamStateChanged: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * The specific state of the output\n\t\t */\n\t\toutputState: string;\n\t};\n\tRecordStateChanged: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * The specific state of the output\n\t\t */\n\t\toutputState: string;\n\t\t/**\n\t\t * File name for the saved recording, if record stopped. `null` otherwise\n\t\t */\n\t\toutputPath: string;\n\t};\n\tRecordFileChanged: {\n\t\t/**\n\t\t * File name that the output has begun writing to\n\t\t */\n\t\tnewOutputPath: string;\n\t};\n\tReplayBufferStateChanged: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * The specific state of the output\n\t\t */\n\t\toutputState: string;\n\t};\n\tVirtualcamStateChanged: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * The specific state of the output\n\t\t */\n\t\toutputState: string;\n\t};\n\tReplayBufferSaved: {\n\t\t/**\n\t\t * Path of the saved replay file\n\t\t */\n\t\tsavedReplayPath: string;\n\t};\n\tSceneItemCreated: {\n\t\t/**\n\t\t * Name of the scene the item was added to\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene the item was added to\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Name of the underlying source (input/scene)\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * UUID of the underlying source (input/scene)\n\t\t */\n\t\tsourceUuid: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * Index position of the item\n\t\t */\n\t\tsceneItemIndex: number;\n\t};\n\tSceneItemRemoved: {\n\t\t/**\n\t\t * Name of the scene the item was removed from\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene the item was removed from\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Name of the underlying source (input/scene)\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * UUID of the underlying source (input/scene)\n\t\t */\n\t\tsourceUuid: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSceneItemListReindexed: {\n\t\t/**\n\t\t * Name of the scene\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Array of scene item objects\n\t\t */\n\t\tsceneItems: JsonObject[];\n\t};\n\tSceneItemEnableStateChanged: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * Whether the scene item is enabled (visible)\n\t\t */\n\t\tsceneItemEnabled: boolean;\n\t};\n\tSceneItemLockStateChanged: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * Whether the scene item is locked\n\t\t */\n\t\tsceneItemLocked: boolean;\n\t};\n\tSceneItemSelected: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSceneItemTransformChanged: {\n\t\t/**\n\t\t * The name of the scene the item is in\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * The UUID of the scene the item is in\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * New transform/crop info of the scene item\n\t\t */\n\t\tsceneItemTransform: JsonObject;\n\t};\n\tSceneCreated: {\n\t\t/**\n\t\t * Name of the new scene\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the new scene\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Whether the new scene is a group\n\t\t */\n\t\tisGroup: boolean;\n\t};\n\tSceneRemoved: {\n\t\t/**\n\t\t * Name of the removed scene\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the removed scene\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Whether the scene was a group\n\t\t */\n\t\tisGroup: boolean;\n\t};\n\tSceneNameChanged: {\n\t\t/**\n\t\t * UUID of the scene\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Old name of the scene\n\t\t */\n\t\toldSceneName: string;\n\t\t/**\n\t\t * New name of the scene\n\t\t */\n\t\tsceneName: string;\n\t};\n\tCurrentProgramSceneChanged: {\n\t\t/**\n\t\t * Name of the scene that was switched to\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene that was switched to\n\t\t */\n\t\tsceneUuid: string;\n\t};\n\tCurrentPreviewSceneChanged: {\n\t\t/**\n\t\t * Name of the scene that was switched to\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * UUID of the scene that was switched to\n\t\t */\n\t\tsceneUuid: string;\n\t};\n\tSceneListChanged: {\n\t\t/**\n\t\t * Updated array of scenes\n\t\t */\n\t\tscenes: JsonObject[];\n\t};\n\tCurrentSceneTransitionChanged: {\n\t\t/**\n\t\t * Name of the new transition\n\t\t */\n\t\ttransitionName: string;\n\t\t/**\n\t\t * UUID of the new transition\n\t\t */\n\t\ttransitionUuid: string;\n\t};\n\tCurrentSceneTransitionDurationChanged: {\n\t\t/**\n\t\t * Transition duration in milliseconds\n\t\t */\n\t\ttransitionDuration: number;\n\t};\n\tSceneTransitionStarted: {\n\t\t/**\n\t\t * Scene transition name\n\t\t */\n\t\ttransitionName: string;\n\t\t/**\n\t\t * Scene transition UUID\n\t\t */\n\t\ttransitionUuid: string;\n\t};\n\tSceneTransitionEnded: {\n\t\t/**\n\t\t * Scene transition name\n\t\t */\n\t\ttransitionName: string;\n\t\t/**\n\t\t * Scene transition UUID\n\t\t */\n\t\ttransitionUuid: string;\n\t};\n\tSceneTransitionVideoEnded: {\n\t\t/**\n\t\t * Scene transition name\n\t\t */\n\t\ttransitionName: string;\n\t\t/**\n\t\t * Scene transition UUID\n\t\t */\n\t\ttransitionUuid: string;\n\t};\n\tStudioModeStateChanged: {\n\t\t/**\n\t\t * True == Enabled, False == Disabled\n\t\t */\n\t\tstudioModeEnabled: boolean;\n\t};\n\tScreenshotSaved: {\n\t\t/**\n\t\t * Path of the saved image file\n\t\t */\n\t\tsavedScreenshotPath: string;\n\t};\n\tVendorEvent: {\n\t\t/**\n\t\t * Name of the vendor emitting the event\n\t\t */\n\t\tvendorName: string;\n\t\t/**\n\t\t * Vendor-provided event typedef\n\t\t */\n\t\teventType: string;\n\t\t/**\n\t\t * Vendor-provided event data. {} if event does not provide any data\n\t\t */\n\t\teventData: JsonObject;\n\t};\n\tCustomEvent: {\n\t\t/**\n\t\t * Custom event data\n\t\t */\n\t\teventData: JsonObject;\n\t};\n}\n\n// Requests and Responses\nexport interface OBSRequestTypes {\n\tGetPersistentData: {\n\t\t/**\n\t\t * The data realm to select. `OBS_WEBSOCKET_DATA_REALM_GLOBAL` or `OBS_WEBSOCKET_DATA_REALM_PROFILE`\n\t\t */\n\t\trealm: string;\n\t\t/**\n\t\t * The name of the slot to retrieve data from\n\t\t */\n\t\tslotName: string;\n\t};\n\tSetPersistentData: {\n\t\t/**\n\t\t * The data realm to select. `OBS_WEBSOCKET_DATA_REALM_GLOBAL` or `OBS_WEBSOCKET_DATA_REALM_PROFILE`\n\t\t */\n\t\trealm: string;\n\t\t/**\n\t\t * The name of the slot to retrieve data from\n\t\t */\n\t\tslotName: string;\n\t\t/**\n\t\t * The value to apply to the slot\n\t\t */\n\t\tslotValue: JsonValue;\n\t};\n\tGetSceneCollectionList: never;\n\tSetCurrentSceneCollection: {\n\t\t/**\n\t\t * Name of the scene collection to switch to\n\t\t */\n\t\tsceneCollectionName: string;\n\t};\n\tCreateSceneCollection: {\n\t\t/**\n\t\t * Name for the new scene collection\n\t\t */\n\t\tsceneCollectionName: string;\n\t};\n\tGetProfileList: never;\n\tSetCurrentProfile: {\n\t\t/**\n\t\t * Name of the profile to switch to\n\t\t */\n\t\tprofileName: string;\n\t};\n\tCreateProfile: {\n\t\t/**\n\t\t * Name for the new profile\n\t\t */\n\t\tprofileName: string;\n\t};\n\tRemoveProfile: {\n\t\t/**\n\t\t * Name of the profile to remove\n\t\t */\n\t\tprofileName: string;\n\t};\n\tGetProfileParameter: {\n\t\t/**\n\t\t * Category of the parameter to get\n\t\t */\n\t\tparameterCategory: string;\n\t\t/**\n\t\t * Name of the parameter to get\n\t\t */\n\t\tparameterName: string;\n\t};\n\tSetProfileParameter: {\n\t\t/**\n\t\t * Category of the parameter to set\n\t\t */\n\t\tparameterCategory: string;\n\t\t/**\n\t\t * Name of the parameter to set\n\t\t */\n\t\tparameterName: string;\n\t\t/**\n\t\t * Value of the parameter to set. Use `null` to delete\n\t\t */\n\t\tparameterValue: string;\n\t};\n\tGetVideoSettings: never;\n\tSetVideoSettings: {\n\t\t/**\n\t\t * Numerator of the fractional FPS value\n\t\t * @restrictions >= 1\n\t\t * @defaultValue Not changed\n\t\t */\n\t\tfpsNumerator?: number;\n\t\t/**\n\t\t * Denominator of the fractional FPS value\n\t\t * @restrictions >= 1\n\t\t * @defaultValue Not changed\n\t\t */\n\t\tfpsDenominator?: number;\n\t\t/**\n\t\t * Width of the base (canvas) resolution in pixels\n\t\t * @restrictions >= 1, <= 4096\n\t\t * @defaultValue Not changed\n\t\t */\n\t\tbaseWidth?: number;\n\t\t/**\n\t\t * Height of the base (canvas) resolution in pixels\n\t\t * @restrictions >= 1, <= 4096\n\t\t * @defaultValue Not changed\n\t\t */\n\t\tbaseHeight?: number;\n\t\t/**\n\t\t * Width of the output resolution in pixels\n\t\t * @restrictions >= 1, <= 4096\n\t\t * @defaultValue Not changed\n\t\t */\n\t\toutputWidth?: number;\n\t\t/**\n\t\t * Height of the output resolution in pixels\n\t\t * @restrictions >= 1, <= 4096\n\t\t * @defaultValue Not changed\n\t\t */\n\t\toutputHeight?: number;\n\t};\n\tGetStreamServiceSettings: never;\n\tSetStreamServiceSettings: {\n\t\t/**\n\t\t * Type of stream service to apply. Example: `rtmp_common` or `rtmp_custom`\n\t\t */\n\t\tstreamServiceType: string;\n\t\t/**\n\t\t * Settings to apply to the service\n\t\t */\n\t\tstreamServiceSettings: JsonObject;\n\t};\n\tGetRecordDirectory: never;\n\tSetRecordDirectory: {\n\t\t/**\n\t\t * Output directory\n\t\t */\n\t\trecordDirectory: string;\n\t};\n\tGetSourceFilterKindList: never;\n\tGetSourceFilterList: {\n\t\t/**\n\t\t * Name of the source\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t};\n\tGetSourceFilterDefaultSettings: {\n\t\t/**\n\t\t * Filter kind to get the default settings for\n\t\t */\n\t\tfilterKind: string;\n\t};\n\tCreateSourceFilter: {\n\t\t/**\n\t\t * Name of the source to add the filter to\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source to add the filter to\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Name of the new filter to be created\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * The kind of filter to be created\n\t\t */\n\t\tfilterKind: string;\n\t\t/**\n\t\t * Settings object to initialize the filter with\n\t\t * @defaultValue Default settings used\n\t\t */\n\t\tfilterSettings?: JsonObject;\n\t};\n\tRemoveSourceFilter: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Name of the filter to remove\n\t\t */\n\t\tfilterName: string;\n\t};\n\tSetSourceFilterName: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Current name of the filter\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * New name for the filter\n\t\t */\n\t\tnewFilterName: string;\n\t};\n\tGetSourceFilter: {\n\t\t/**\n\t\t * Name of the source\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t};\n\tSetSourceFilterIndex: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * New index position of the filter\n\t\t * @restrictions >= 0\n\t\t */\n\t\tfilterIndex: number;\n\t};\n\tSetSourceFilterSettings: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Name of the filter to set the settings of\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * Object of settings to apply\n\t\t */\n\t\tfilterSettings: JsonObject;\n\t\t/**\n\t\t * True == apply the settings on top of existing ones, False == reset the input to its defaults, then apply settings.\n\t\t * @defaultValue true\n\t\t */\n\t\toverlay?: boolean;\n\t};\n\tSetSourceFilterEnabled: {\n\t\t/**\n\t\t * Name of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source the filter is on\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Name of the filter\n\t\t */\n\t\tfilterName: string;\n\t\t/**\n\t\t * New enable state of the filter\n\t\t */\n\t\tfilterEnabled: boolean;\n\t};\n\tGetVersion: never;\n\tGetStats: never;\n\tBroadcastCustomEvent: {\n\t\t/**\n\t\t * Data payload to emit to all receivers\n\t\t */\n\t\teventData: JsonObject;\n\t};\n\tCallVendorRequest: {\n\t\t/**\n\t\t * Name of the vendor to use\n\t\t */\n\t\tvendorName: string;\n\t\t/**\n\t\t * The request type to call\n\t\t */\n\t\trequestType: string;\n\t\t/**\n\t\t * Object containing appropriate request data\n\t\t * @defaultValue {}\n\t\t */\n\t\trequestData?: JsonObject;\n\t};\n\tGetHotkeyList: never;\n\tTriggerHotkeyByName: {\n\t\t/**\n\t\t * Name of the hotkey to trigger\n\t\t */\n\t\thotkeyName: string;\n\t\t/**\n\t\t * Name of context of the hotkey to trigger\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tcontextName?: string;\n\t};\n\tTriggerHotkeyByKeySequence: {\n\t\t/**\n\t\t * The OBS key ID to use. See https://github.com/obsproject/obs-studio/blob/master/libobs/obs-hotkeys.h\n\t\t * @defaultValue Not pressed\n\t\t */\n\t\tkeyId?: string;\n\t\t/**\n\t\t * Object containing key modifiers to apply\n\t\t * @defaultValue Ignored\n\t\t */\n\t\tkeyModifiers?: {\n\t\t\t/**\n\t\t\t * Press Shift\n\t\t\t * @defaultValue Not pressed\n\t\t\t */\n\t\t\tshift?: boolean;\n\t\t\t/**\n\t\t\t * Press CTRL\n\t\t\t * @defaultValue Not pressed\n\t\t\t */\n\t\t\tcontrol?: boolean;\n\t\t\t/**\n\t\t\t * Press ALT\n\t\t\t * @defaultValue Not pressed\n\t\t\t */\n\t\t\talt?: boolean;\n\t\t\t/**\n\t\t\t * Press CMD (Mac)\n\t\t\t * @defaultValue Not pressed\n\t\t\t */\n\t\t\tcommand?: boolean;\n\t\t};\n\t};\n\tSleep: {\n\t\t/**\n\t\t * Number of milliseconds to sleep for (if `SERIAL_REALTIME` mode)\n\t\t * @restrictions >= 0, <= 50000\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsleepMillis?: number;\n\t\t/**\n\t\t * Number of frames to sleep for (if `SERIAL_FRAME` mode)\n\t\t * @restrictions >= 0, <= 10000\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsleepFrames?: number;\n\t};\n\tGetInputList: {\n\t\t/**\n\t\t * Restrict the array to only inputs of the specified kind\n\t\t * @defaultValue All kinds included\n\t\t */\n\t\tinputKind?: string;\n\t};\n\tGetInputKindList: {\n\t\t/**\n\t\t * True == Return all kinds as unversioned, False == Return with version suffixes (if available)\n\t\t * @defaultValue false\n\t\t */\n\t\tunversioned?: boolean;\n\t};\n\tGetSpecialInputs: never;\n\tCreateInput: {\n\t\t/**\n\t\t * Name of the scene to add the input to as a scene item\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene to add the input to as a scene item\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Name of the new input to created\n\t\t */\n\t\tinputName: string;\n\t\t/**\n\t\t * The kind of input to be created\n\t\t */\n\t\tinputKind: string;\n\t\t/**\n\t\t * Settings object to initialize the input with\n\t\t * @defaultValue Default settings used\n\t\t */\n\t\tinputSettings?: JsonObject;\n\t\t/**\n\t\t * Whether to set the created scene item to enabled or disabled\n\t\t * @defaultValue True\n\t\t */\n\t\tsceneItemEnabled?: boolean;\n\t};\n\tRemoveInput: {\n\t\t/**\n\t\t * Name of the input to remove\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to remove\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputName: {\n\t\t/**\n\t\t * Current input name\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * Current input UUID\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * New name for the input\n\t\t */\n\t\tnewInputName: string;\n\t};\n\tGetInputDefaultSettings: {\n\t\t/**\n\t\t * Input kind to get the default settings for\n\t\t */\n\t\tinputKind: string;\n\t};\n\tGetInputSettings: {\n\t\t/**\n\t\t * Name of the input to get the settings of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to get the settings of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputSettings: {\n\t\t/**\n\t\t * Name of the input to set the settings of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to set the settings of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Object of settings to apply\n\t\t */\n\t\tinputSettings: JsonObject;\n\t\t/**\n\t\t * True == apply the settings on top of existing ones, False == reset the input to its defaults, then apply settings.\n\t\t * @defaultValue true\n\t\t */\n\t\toverlay?: boolean;\n\t};\n\tGetInputMute: {\n\t\t/**\n\t\t * Name of input to get the mute state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of input to get the mute state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputMute: {\n\t\t/**\n\t\t * Name of the input to set the mute state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to set the mute state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Whether to mute the input or not\n\t\t */\n\t\tinputMuted: boolean;\n\t};\n\tToggleInputMute: {\n\t\t/**\n\t\t * Name of the input to toggle the mute state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to toggle the mute state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tGetInputVolume: {\n\t\t/**\n\t\t * Name of the input to get the volume of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to get the volume of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputVolume: {\n\t\t/**\n\t\t * Name of the input to set the volume of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to set the volume of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Volume setting in mul\n\t\t * @restrictions >= 0, <= 20\n\t\t * @defaultValue `inputVolumeDb` should be specified\n\t\t */\n\t\tinputVolumeMul?: number;\n\t\t/**\n\t\t * Volume setting in dB\n\t\t * @restrictions >= -100, <= 26\n\t\t * @defaultValue `inputVolumeMul` should be specified\n\t\t */\n\t\tinputVolumeDb?: number;\n\t};\n\tGetInputAudioBalance: {\n\t\t/**\n\t\t * Name of the input to get the audio balance of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to get the audio balance of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputAudioBalance: {\n\t\t/**\n\t\t * Name of the input to set the audio balance of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to set the audio balance of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * New audio balance value\n\t\t * @restrictions >= 0.0, <= 1.0\n\t\t */\n\t\tinputAudioBalance: number;\n\t};\n\tGetInputAudioSyncOffset: {\n\t\t/**\n\t\t * Name of the input to get the audio sync offset of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to get the audio sync offset of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputAudioSyncOffset: {\n\t\t/**\n\t\t * Name of the input to set the audio sync offset of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to set the audio sync offset of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * New audio sync offset in milliseconds\n\t\t * @restrictions >= -950, <= 20000\n\t\t */\n\t\tinputAudioSyncOffset: number;\n\t};\n\tGetInputAudioMonitorType: {\n\t\t/**\n\t\t * Name of the input to get the audio monitor type of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to get the audio monitor type of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputAudioMonitorType: {\n\t\t/**\n\t\t * Name of the input to set the audio monitor type of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to set the audio monitor type of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Audio monitor type\n\t\t */\n\t\tmonitorType: string;\n\t};\n\tGetInputAudioTracks: {\n\t\t/**\n\t\t * Name of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetInputAudioTracks: {\n\t\t/**\n\t\t * Name of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Track settings to apply\n\t\t */\n\t\tinputAudioTracks: JsonObject;\n\t};\n\tGetInputPropertiesListPropertyItems: {\n\t\t/**\n\t\t * Name of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Name of the list property to get the items of\n\t\t */\n\t\tpropertyName: string;\n\t};\n\tPressInputPropertiesButton: {\n\t\t/**\n\t\t * Name of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Name of the button property to press\n\t\t */\n\t\tpropertyName: string;\n\t};\n\tGetMediaInputStatus: {\n\t\t/**\n\t\t * Name of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tSetMediaInputCursor: {\n\t\t/**\n\t\t * Name of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * New cursor position to set\n\t\t * @restrictions >= 0\n\t\t */\n\t\tmediaCursor: number;\n\t};\n\tOffsetMediaInputCursor: {\n\t\t/**\n\t\t * Name of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Value to offset the current cursor position by\n\t\t */\n\t\tmediaCursorOffset: number;\n\t};\n\tTriggerMediaInputAction: {\n\t\t/**\n\t\t * Name of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the media input\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t\t/**\n\t\t * Identifier of the `ObsMediaInputAction` enum\n\t\t */\n\t\tmediaAction: string;\n\t};\n\tGetVirtualCamStatus: never;\n\tToggleVirtualCam: never;\n\tStartVirtualCam: never;\n\tStopVirtualCam: never;\n\tGetReplayBufferStatus: never;\n\tToggleReplayBuffer: never;\n\tStartReplayBuffer: never;\n\tStopReplayBuffer: never;\n\tSaveReplayBuffer: never;\n\tGetLastReplayBufferReplay: never;\n\tGetOutputList: never;\n\tGetOutputStatus: {\n\t\t/**\n\t\t * Output name\n\t\t */\n\t\toutputName: string;\n\t};\n\tToggleOutput: {\n\t\t/**\n\t\t * Output name\n\t\t */\n\t\toutputName: string;\n\t};\n\tStartOutput: {\n\t\t/**\n\t\t * Output name\n\t\t */\n\t\toutputName: string;\n\t};\n\tStopOutput: {\n\t\t/**\n\t\t * Output name\n\t\t */\n\t\toutputName: string;\n\t};\n\tGetOutputSettings: {\n\t\t/**\n\t\t * Output name\n\t\t */\n\t\toutputName: string;\n\t};\n\tSetOutputSettings: {\n\t\t/**\n\t\t * Output name\n\t\t */\n\t\toutputName: string;\n\t\t/**\n\t\t * Output settings\n\t\t */\n\t\toutputSettings: JsonObject;\n\t};\n\tGetRecordStatus: never;\n\tToggleRecord: never;\n\tStartRecord: never;\n\tStopRecord: never;\n\tToggleRecordPause: never;\n\tPauseRecord: never;\n\tResumeRecord: never;\n\tSplitRecordFile: never;\n\tCreateRecordChapter: {\n\t\t/**\n\t\t * Name of the new chapter\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tchapterName?: string;\n\t};\n\tGetSceneItemList: {\n\t\t/**\n\t\t * Name of the scene to get the items of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene to get the items of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t};\n\tGetGroupSceneItemList: {\n\t\t/**\n\t\t * Name of the group to get the items of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the group to get the items of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t};\n\tGetSceneItemId: {\n\t\t/**\n\t\t * Name of the scene or group to search in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene or group to search in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Name of the source to find\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * Number of matches to skip during search. >= 0 means first forward. -1 means last (top) item\n\t\t * @restrictions >= -1\n\t\t * @defaultValue 0\n\t\t */\n\t\tsearchOffset?: number;\n\t};\n\tGetSceneItemSource: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tCreateSceneItem: {\n\t\t/**\n\t\t * Name of the scene to create the new item in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene to create the new item in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Name of the source to add to the scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source to add to the scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Enable state to apply to the scene item on creation\n\t\t * @defaultValue True\n\t\t */\n\t\tsceneItemEnabled?: boolean;\n\t};\n\tRemoveSceneItem: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tDuplicateSceneItem: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * Name of the scene to create the duplicated item in\n\t\t * @defaultValue From scene is assumed\n\t\t */\n\t\tdestinationSceneName?: string;\n\t\t/**\n\t\t * UUID of the scene to create the duplicated item in\n\t\t * @defaultValue From scene is assumed\n\t\t */\n\t\tdestinationSceneUuid?: string;\n\t};\n\tGetSceneItemTransform: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSetSceneItemTransform: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * Object containing scene item transform info to update\n\t\t */\n\t\tsceneItemTransform: JsonObject;\n\t};\n\tGetSceneItemEnabled: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSetSceneItemEnabled: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * New enable state of the scene item\n\t\t */\n\t\tsceneItemEnabled: boolean;\n\t};\n\tGetSceneItemLocked: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSetSceneItemLocked: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * New lock state of the scene item\n\t\t */\n\t\tsceneItemLocked: boolean;\n\t};\n\tGetSceneItemIndex: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSetSceneItemIndex: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * New index position of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemIndex: number;\n\t};\n\tGetSceneItemBlendMode: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tSetSceneItemBlendMode: {\n\t\t/**\n\t\t * Name of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene the item is in\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t * @restrictions >= 0\n\t\t */\n\t\tsceneItemId: number;\n\t\t/**\n\t\t * New blend mode\n\t\t */\n\t\tsceneItemBlendMode: string;\n\t};\n\tGetSceneList: never;\n\tGetGroupList: never;\n\tGetCurrentProgramScene: never;\n\tSetCurrentProgramScene: {\n\t\t/**\n\t\t * Scene name to set as the current program scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * Scene UUID to set as the current program scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t};\n\tGetCurrentPreviewScene: never;\n\tSetCurrentPreviewScene: {\n\t\t/**\n\t\t * Scene name to set as the current preview scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * Scene UUID to set as the current preview scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t};\n\tCreateScene: {\n\t\t/**\n\t\t * Name for the new scene\n\t\t */\n\t\tsceneName: string;\n\t};\n\tRemoveScene: {\n\t\t/**\n\t\t * Name of the scene to remove\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene to remove\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t};\n\tSetSceneName: {\n\t\t/**\n\t\t * Name of the scene to be renamed\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene to be renamed\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * New name for the scene\n\t\t */\n\t\tnewSceneName: string;\n\t};\n\tGetSceneSceneTransitionOverride: {\n\t\t/**\n\t\t * Name of the scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t};\n\tSetSceneSceneTransitionOverride: {\n\t\t/**\n\t\t * Name of the scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneName?: string;\n\t\t/**\n\t\t * UUID of the scene\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsceneUuid?: string;\n\t\t/**\n\t\t * Name of the scene transition to use as override. Specify `null` to remove\n\t\t * @defaultValue Unchanged\n\t\t */\n\t\ttransitionName?: string;\n\t\t/**\n\t\t * Duration to use for any overridden transition. Specify `null` to remove\n\t\t * @restrictions >= 50, <= 20000\n\t\t * @defaultValue Unchanged\n\t\t */\n\t\ttransitionDuration?: number;\n\t};\n\tGetSourceActive: {\n\t\t/**\n\t\t * Name of the source to get the active state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source to get the active state of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t};\n\tGetSourceScreenshot: {\n\t\t/**\n\t\t * Name of the source to take a screenshot of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source to take a screenshot of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Image compression format to use. Use `GetVersion` to get compatible image formats\n\t\t */\n\t\timageFormat: string;\n\t\t/**\n\t\t * Width to scale the screenshot to\n\t\t * @restrictions >= 8, <= 4096\n\t\t * @defaultValue Source value is used\n\t\t */\n\t\timageWidth?: number;\n\t\t/**\n\t\t * Height to scale the screenshot to\n\t\t * @restrictions >= 8, <= 4096\n\t\t * @defaultValue Source value is used\n\t\t */\n\t\timageHeight?: number;\n\t\t/**\n\t\t * Compression quality to use. 0 for high compression, 100 for uncompressed. -1 to use \"default\" (whatever that means, idk)\n\t\t * @restrictions >= -1, <= 100\n\t\t * @defaultValue -1\n\t\t */\n\t\timageCompressionQuality?: number;\n\t};\n\tSaveSourceScreenshot: {\n\t\t/**\n\t\t * Name of the source to take a screenshot of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source to take a screenshot of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Image compression format to use. Use `GetVersion` to get compatible image formats\n\t\t */\n\t\timageFormat: string;\n\t\t/**\n\t\t * Path to save the screenshot file to. Eg. `C:\\Users\\<USER>\\Desktop\\screenshot.png`\n\t\t */\n\t\timageFilePath: string;\n\t\t/**\n\t\t * Width to scale the screenshot to\n\t\t * @restrictions >= 8, <= 4096\n\t\t * @defaultValue Source value is used\n\t\t */\n\t\timageWidth?: number;\n\t\t/**\n\t\t * Height to scale the screenshot to\n\t\t * @restrictions >= 8, <= 4096\n\t\t * @defaultValue Source value is used\n\t\t */\n\t\timageHeight?: number;\n\t\t/**\n\t\t * Compression quality to use. 0 for high compression, 100 for uncompressed. -1 to use \"default\" (whatever that means, idk)\n\t\t * @restrictions >= -1, <= 100\n\t\t * @defaultValue -1\n\t\t */\n\t\timageCompressionQuality?: number;\n\t};\n\tGetStreamStatus: never;\n\tToggleStream: never;\n\tStartStream: never;\n\tStopStream: never;\n\tSendStreamCaption: {\n\t\t/**\n\t\t * Caption text\n\t\t */\n\t\tcaptionText: string;\n\t};\n\tGetTransitionKindList: never;\n\tGetSceneTransitionList: never;\n\tGetCurrentSceneTransition: never;\n\tSetCurrentSceneTransition: {\n\t\t/**\n\t\t * Name of the transition to make active\n\t\t */\n\t\ttransitionName: string;\n\t};\n\tSetCurrentSceneTransitionDuration: {\n\t\t/**\n\t\t * Duration in milliseconds\n\t\t * @restrictions >= 50, <= 20000\n\t\t */\n\t\ttransitionDuration: number;\n\t};\n\tSetCurrentSceneTransitionSettings: {\n\t\t/**\n\t\t * Settings object to apply to the transition. Can be `{}`\n\t\t */\n\t\ttransitionSettings: JsonObject;\n\t\t/**\n\t\t * Whether to overlay over the current settings or replace them\n\t\t * @defaultValue true\n\t\t */\n\t\toverlay?: boolean;\n\t};\n\tGetCurrentSceneTransitionCursor: never;\n\tTriggerStudioModeTransition: never;\n\tSetTBarPosition: {\n\t\t/**\n\t\t * New position\n\t\t * @restrictions >= 0.0, <= 1.0\n\t\t */\n\t\tposition: number;\n\t\t/**\n\t\t * Whether to release the TBar. Only set `false` if you know that you will be sending another position update\n\t\t * @defaultValue `true`\n\t\t */\n\t\trelease?: boolean;\n\t};\n\tGetStudioModeEnabled: never;\n\tSetStudioModeEnabled: {\n\t\t/**\n\t\t * True == Enabled, False == Disabled\n\t\t */\n\t\tstudioModeEnabled: boolean;\n\t};\n\tOpenInputPropertiesDialog: {\n\t\t/**\n\t\t * Name of the input to open the dialog of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to open the dialog of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tOpenInputFiltersDialog: {\n\t\t/**\n\t\t * Name of the input to open the dialog of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to open the dialog of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tOpenInputInteractDialog: {\n\t\t/**\n\t\t * Name of the input to open the dialog of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputName?: string;\n\t\t/**\n\t\t * UUID of the input to open the dialog of\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tinputUuid?: string;\n\t};\n\tGetMonitorList: never;\n\tOpenVideoMixProjector: {\n\t\t/**\n\t\t * Type of mix to open\n\t\t */\n\t\tvideoMixType: string;\n\t\t/**\n\t\t * Monitor index, use `GetMonitorList` to obtain index\n\t\t * @defaultValue -1: Opens projector in windowed mode\n\t\t */\n\t\tmonitorIndex?: number;\n\t\t/**\n\t\t * Size/Position data for a windowed projector, in Qt Base64 encoded format. Mutually exclusive with `monitorIndex`\n\t\t * @defaultValue N/A\n\t\t */\n\t\tprojectorGeometry?: string;\n\t};\n\tOpenSourceProjector: {\n\t\t/**\n\t\t * Name of the source to open a projector for\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceName?: string;\n\t\t/**\n\t\t * UUID of the source to open a projector for\n\t\t * @defaultValue Unknown\n\t\t */\n\t\tsourceUuid?: string;\n\t\t/**\n\t\t * Monitor index, use `GetMonitorList` to obtain index\n\t\t * @defaultValue -1: Opens projector in windowed mode\n\t\t */\n\t\tmonitorIndex?: number;\n\t\t/**\n\t\t * Size/Position data for a windowed projector, in Qt Base64 encoded format. Mutually exclusive with `monitorIndex`\n\t\t * @defaultValue N/A\n\t\t */\n\t\tprojectorGeometry?: string;\n\t};\n}\n\nexport interface OBSResponseTypes {\n\tGetPersistentData: {\n\t\t/**\n\t\t * Value associated with the slot. `null` if not set\n\t\t */\n\t\tslotValue: JsonValue;\n\t};\n\tSetPersistentData: undefined;\n\tGetSceneCollectionList: {\n\t\t/**\n\t\t * The name of the current scene collection\n\t\t */\n\t\tcurrentSceneCollectionName: string;\n\t\t/**\n\t\t * Array of all available scene collections\n\t\t */\n\t\tsceneCollections: string[];\n\t};\n\tSetCurrentSceneCollection: undefined;\n\tCreateSceneCollection: undefined;\n\tGetProfileList: {\n\t\t/**\n\t\t * The name of the current profile\n\t\t */\n\t\tcurrentProfileName: string;\n\t\t/**\n\t\t * Array of all available profiles\n\t\t */\n\t\tprofiles: string[];\n\t};\n\tSetCurrentProfile: undefined;\n\tCreateProfile: undefined;\n\tRemoveProfile: undefined;\n\tGetProfileParameter: {\n\t\t/**\n\t\t * Value associated with the parameter. `null` if not set and no default\n\t\t */\n\t\tparameterValue: string;\n\t\t/**\n\t\t * Default value associated with the parameter. `null` if no default\n\t\t */\n\t\tdefaultParameterValue: string;\n\t};\n\tSetProfileParameter: undefined;\n\tGetVideoSettings: {\n\t\t/**\n\t\t * Numerator of the fractional FPS value\n\t\t */\n\t\tfpsNumerator: number;\n\t\t/**\n\t\t * Denominator of the fractional FPS value\n\t\t */\n\t\tfpsDenominator: number;\n\t\t/**\n\t\t * Width of the base (canvas) resolution in pixels\n\t\t */\n\t\tbaseWidth: number;\n\t\t/**\n\t\t * Height of the base (canvas) resolution in pixels\n\t\t */\n\t\tbaseHeight: number;\n\t\t/**\n\t\t * Width of the output resolution in pixels\n\t\t */\n\t\toutputWidth: number;\n\t\t/**\n\t\t * Height of the output resolution in pixels\n\t\t */\n\t\toutputHeight: number;\n\t};\n\tSetVideoSettings: undefined;\n\tGetStreamServiceSettings: {\n\t\t/**\n\t\t * Stream service type, like `rtmp_custom` or `rtmp_common`\n\t\t */\n\t\tstreamServiceType: string;\n\t\t/**\n\t\t * Stream service settings\n\t\t */\n\t\tstreamServiceSettings: JsonObject;\n\t};\n\tSetStreamServiceSettings: undefined;\n\tGetRecordDirectory: {\n\t\t/**\n\t\t * Output directory\n\t\t */\n\t\trecordDirectory: string;\n\t};\n\tSetRecordDirectory: undefined;\n\tGetSourceFilterKindList: {\n\t\t/**\n\t\t * Array of source filter kinds\n\t\t */\n\t\tsourceFilterKinds: string[];\n\t};\n\tGetSourceFilterList: {\n\t\t/**\n\t\t * Array of filters\n\t\t */\n\t\tfilters: JsonObject[];\n\t};\n\tGetSourceFilterDefaultSettings: {\n\t\t/**\n\t\t * Object of default settings for the filter kind\n\t\t */\n\t\tdefaultFilterSettings: JsonObject;\n\t};\n\tCreateSourceFilter: undefined;\n\tRemoveSourceFilter: undefined;\n\tSetSourceFilterName: undefined;\n\tGetSourceFilter: {\n\t\t/**\n\t\t * Whether the filter is enabled\n\t\t */\n\t\tfilterEnabled: boolean;\n\t\t/**\n\t\t * Index of the filter in the list, beginning at 0\n\t\t */\n\t\tfilterIndex: number;\n\t\t/**\n\t\t * The kind of filter\n\t\t */\n\t\tfilterKind: string;\n\t\t/**\n\t\t * Settings object associated with the filter\n\t\t */\n\t\tfilterSettings: JsonObject;\n\t};\n\tSetSourceFilterIndex: undefined;\n\tSetSourceFilterSettings: undefined;\n\tSetSourceFilterEnabled: undefined;\n\tGetVersion: {\n\t\t/**\n\t\t * Current OBS Studio version\n\t\t */\n\t\tobsVersion: string;\n\t\t/**\n\t\t * Current obs-websocket version\n\t\t */\n\t\tobsWebSocketVersion: string;\n\t\t/**\n\t\t * Current latest obs-websocket RPC version\n\t\t */\n\t\trpcVersion: number;\n\t\t/**\n\t\t * Array of available RPC requests for the currently negotiated RPC version\n\t\t */\n\t\tavailableRequests: string[];\n\t\t/**\n\t\t * Image formats available in `GetSourceScreenshot` and `SaveSourceScreenshot` requests.\n\t\t */\n\t\tsupportedImageFormats: string[];\n\t\t/**\n\t\t * Name of the platform. Usually `windows`, `macos`, or `ubuntu` (linux flavor). Not guaranteed to be any of those\n\t\t */\n\t\tplatform: string;\n\t\t/**\n\t\t * Description of the platform, like `Windows 10 (10.0)`\n\t\t */\n\t\tplatformDescription: string;\n\t};\n\tGetStats: {\n\t\t/**\n\t\t * Current CPU usage in percent\n\t\t */\n\t\tcpuUsage: number;\n\t\t/**\n\t\t * Amount of memory in MB currently being used by OBS\n\t\t */\n\t\tmemoryUsage: number;\n\t\t/**\n\t\t * Available disk space on the device being used for recording storage\n\t\t */\n\t\tavailableDiskSpace: number;\n\t\t/**\n\t\t * Current FPS being rendered\n\t\t */\n\t\tactiveFps: number;\n\t\t/**\n\t\t * Average time in milliseconds that OBS is taking to render a frame\n\t\t */\n\t\taverageFrameRenderTime: number;\n\t\t/**\n\t\t * Number of frames skipped by OBS in the render thread\n\t\t */\n\t\trenderSkippedFrames: number;\n\t\t/**\n\t\t * Total number of frames outputted by the render thread\n\t\t */\n\t\trenderTotalFrames: number;\n\t\t/**\n\t\t * Number of frames skipped by OBS in the output thread\n\t\t */\n\t\toutputSkippedFrames: number;\n\t\t/**\n\t\t * Total number of frames outputted by the output thread\n\t\t */\n\t\toutputTotalFrames: number;\n\t\t/**\n\t\t * Total number of messages received by obs-websocket from the client\n\t\t */\n\t\twebSocketSessionIncomingMessages: number;\n\t\t/**\n\t\t * Total number of messages sent by obs-websocket to the client\n\t\t */\n\t\twebSocketSessionOutgoingMessages: number;\n\t};\n\tBroadcastCustomEvent: undefined;\n\tCallVendorRequest: {\n\t\t/**\n\t\t * Echoed of `vendorName`\n\t\t */\n\t\tvendorName: string;\n\t\t/**\n\t\t * Echoed of `requestType`\n\t\t */\n\t\trequestType: string;\n\t\t/**\n\t\t * Object containing appropriate response data. {} if request does not provide any response data\n\t\t */\n\t\tresponseData: JsonObject;\n\t};\n\tGetHotkeyList: {\n\t\t/**\n\t\t * Array of hotkey names\n\t\t */\n\t\thotkeys: string[];\n\t};\n\tTriggerHotkeyByName: undefined;\n\tTriggerHotkeyByKeySequence: undefined;\n\tSleep: undefined;\n\tGetInputList: {\n\t\t/**\n\t\t * Array of inputs\n\t\t */\n\t\tinputs: JsonObject[];\n\t};\n\tGetInputKindList: {\n\t\t/**\n\t\t * Array of input kinds\n\t\t */\n\t\tinputKinds: string[];\n\t};\n\tGetSpecialInputs: {\n\t\t/**\n\t\t * Name of the Desktop Audio input\n\t\t */\n\t\tdesktop1: string;\n\t\t/**\n\t\t * Name of the Desktop Audio 2 input\n\t\t */\n\t\tdesktop2: string;\n\t\t/**\n\t\t * Name of the Mic/Auxiliary Audio input\n\t\t */\n\t\tmic1: string;\n\t\t/**\n\t\t * Name of the Mic/Auxiliary Audio 2 input\n\t\t */\n\t\tmic2: string;\n\t\t/**\n\t\t * Name of the Mic/Auxiliary Audio 3 input\n\t\t */\n\t\tmic3: string;\n\t\t/**\n\t\t * Name of the Mic/Auxiliary Audio 4 input\n\t\t */\n\t\tmic4: string;\n\t};\n\tCreateInput: {\n\t\t/**\n\t\t * UUID of the newly created input\n\t\t */\n\t\tinputUuid: string;\n\t\t/**\n\t\t * ID of the newly created scene item\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tRemoveInput: undefined;\n\tSetInputName: undefined;\n\tGetInputDefaultSettings: {\n\t\t/**\n\t\t * Object of default settings for the input kind\n\t\t */\n\t\tdefaultInputSettings: JsonObject;\n\t};\n\tGetInputSettings: {\n\t\t/**\n\t\t * Object of settings for the input\n\t\t */\n\t\tinputSettings: JsonObject;\n\t\t/**\n\t\t * The kind of the input\n\t\t */\n\t\tinputKind: string;\n\t};\n\tSetInputSettings: undefined;\n\tGetInputMute: {\n\t\t/**\n\t\t * Whether the input is muted\n\t\t */\n\t\tinputMuted: boolean;\n\t};\n\tSetInputMute: undefined;\n\tToggleInputMute: {\n\t\t/**\n\t\t * Whether the input has been muted or unmuted\n\t\t */\n\t\tinputMuted: boolean;\n\t};\n\tGetInputVolume: {\n\t\t/**\n\t\t * Volume setting in mul\n\t\t */\n\t\tinputVolumeMul: number;\n\t\t/**\n\t\t * Volume setting in dB\n\t\t */\n\t\tinputVolumeDb: number;\n\t};\n\tSetInputVolume: undefined;\n\tGetInputAudioBalance: {\n\t\t/**\n\t\t * Audio balance value from 0.0-1.0\n\t\t */\n\t\tinputAudioBalance: number;\n\t};\n\tSetInputAudioBalance: undefined;\n\tGetInputAudioSyncOffset: {\n\t\t/**\n\t\t * Audio sync offset in milliseconds\n\t\t */\n\t\tinputAudioSyncOffset: number;\n\t};\n\tSetInputAudioSyncOffset: undefined;\n\tGetInputAudioMonitorType: {\n\t\t/**\n\t\t * Audio monitor type\n\t\t */\n\t\tmonitorType: string;\n\t};\n\tSetInputAudioMonitorType: undefined;\n\tGetInputAudioTracks: {\n\t\t/**\n\t\t * Object of audio tracks and associated enable states\n\t\t */\n\t\tinputAudioTracks: JsonObject;\n\t};\n\tSetInputAudioTracks: undefined;\n\tGetInputPropertiesListPropertyItems: {\n\t\t/**\n\t\t * Array of items in the list property\n\t\t */\n\t\tpropertyItems: JsonObject[];\n\t};\n\tPressInputPropertiesButton: undefined;\n\tGetMediaInputStatus: {\n\t\t/**\n\t\t * State of the media input\n\t\t */\n\t\tmediaState: string;\n\t\t/**\n\t\t * Total duration of the playing media in milliseconds. `null` if not playing\n\t\t */\n\t\tmediaDuration: number;\n\t\t/**\n\t\t * Position of the cursor in milliseconds. `null` if not playing\n\t\t */\n\t\tmediaCursor: number;\n\t};\n\tSetMediaInputCursor: undefined;\n\tOffsetMediaInputCursor: undefined;\n\tTriggerMediaInputAction: undefined;\n\tGetVirtualCamStatus: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tToggleVirtualCam: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tStartVirtualCam: undefined;\n\tStopVirtualCam: undefined;\n\tGetReplayBufferStatus: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tToggleReplayBuffer: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tStartReplayBuffer: undefined;\n\tStopReplayBuffer: undefined;\n\tSaveReplayBuffer: undefined;\n\tGetLastReplayBufferReplay: {\n\t\t/**\n\t\t * File path\n\t\t */\n\t\tsavedReplayPath: string;\n\t};\n\tGetOutputList: {\n\t\t/**\n\t\t * Array of outputs\n\t\t */\n\t\toutputs: JsonObject[];\n\t};\n\tGetOutputStatus: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * Whether the output is reconnecting\n\t\t */\n\t\toutputReconnecting: boolean;\n\t\t/**\n\t\t * Current formatted timecode string for the output\n\t\t */\n\t\toutputTimecode: string;\n\t\t/**\n\t\t * Current duration in milliseconds for the output\n\t\t */\n\t\toutputDuration: number;\n\t\t/**\n\t\t * Congestion of the output\n\t\t */\n\t\toutputCongestion: number;\n\t\t/**\n\t\t * Number of bytes sent by the output\n\t\t */\n\t\toutputBytes: number;\n\t\t/**\n\t\t * Number of frames skipped by the output's process\n\t\t */\n\t\toutputSkippedFrames: number;\n\t\t/**\n\t\t * Total number of frames delivered by the output's process\n\t\t */\n\t\toutputTotalFrames: number;\n\t};\n\tToggleOutput: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tStartOutput: undefined;\n\tStopOutput: undefined;\n\tGetOutputSettings: {\n\t\t/**\n\t\t * Output settings\n\t\t */\n\t\toutputSettings: JsonObject;\n\t};\n\tSetOutputSettings: undefined;\n\tGetRecordStatus: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * Whether the output is paused\n\t\t */\n\t\toutputPaused: boolean;\n\t\t/**\n\t\t * Current formatted timecode string for the output\n\t\t */\n\t\toutputTimecode: string;\n\t\t/**\n\t\t * Current duration in milliseconds for the output\n\t\t */\n\t\toutputDuration: number;\n\t\t/**\n\t\t * Number of bytes sent by the output\n\t\t */\n\t\toutputBytes: number;\n\t};\n\tToggleRecord: {\n\t\t/**\n\t\t * The new active state of the output\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tStartRecord: undefined;\n\tStopRecord: {\n\t\t/**\n\t\t * File name for the saved recording\n\t\t */\n\t\toutputPath: string;\n\t};\n\tToggleRecordPause: undefined;\n\tPauseRecord: undefined;\n\tResumeRecord: undefined;\n\tSplitRecordFile: undefined;\n\tCreateRecordChapter: undefined;\n\tGetSceneItemList: {\n\t\t/**\n\t\t * Array of scene items in the scene\n\t\t */\n\t\tsceneItems: JsonObject[];\n\t};\n\tGetGroupSceneItemList: {\n\t\t/**\n\t\t * Array of scene items in the group\n\t\t */\n\t\tsceneItems: JsonObject[];\n\t};\n\tGetSceneItemId: {\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tGetSceneItemSource: {\n\t\t/**\n\t\t * Name of the source associated with the scene item\n\t\t */\n\t\tsourceName: string;\n\t\t/**\n\t\t * UUID of the source associated with the scene item\n\t\t */\n\t\tsourceUuid: string;\n\t};\n\tCreateSceneItem: {\n\t\t/**\n\t\t * Numeric ID of the scene item\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tRemoveSceneItem: undefined;\n\tDuplicateSceneItem: {\n\t\t/**\n\t\t * Numeric ID of the duplicated scene item\n\t\t */\n\t\tsceneItemId: number;\n\t};\n\tGetSceneItemTransform: {\n\t\t/**\n\t\t * Object containing scene item transform info\n\t\t */\n\t\tsceneItemTransform: JsonObject;\n\t};\n\tSetSceneItemTransform: undefined;\n\tGetSceneItemEnabled: {\n\t\t/**\n\t\t * Whether the scene item is enabled. `true` for enabled, `false` for disabled\n\t\t */\n\t\tsceneItemEnabled: boolean;\n\t};\n\tSetSceneItemEnabled: undefined;\n\tGetSceneItemLocked: {\n\t\t/**\n\t\t * Whether the scene item is locked. `true` for locked, `false` for unlocked\n\t\t */\n\t\tsceneItemLocked: boolean;\n\t};\n\tSetSceneItemLocked: undefined;\n\tGetSceneItemIndex: {\n\t\t/**\n\t\t * Index position of the scene item\n\t\t */\n\t\tsceneItemIndex: number;\n\t};\n\tSetSceneItemIndex: undefined;\n\tGetSceneItemBlendMode: {\n\t\t/**\n\t\t * Current blend mode\n\t\t */\n\t\tsceneItemBlendMode: string;\n\t};\n\tSetSceneItemBlendMode: undefined;\n\tGetSceneList: {\n\t\t/**\n\t\t * Current program scene name. Can be `null` if internal state desync\n\t\t */\n\t\tcurrentProgramSceneName: string;\n\t\t/**\n\t\t * Current program scene UUID. Can be `null` if internal state desync\n\t\t */\n\t\tcurrentProgramSceneUuid: string;\n\t\t/**\n\t\t * Current preview scene name. `null` if not in studio mode\n\t\t */\n\t\tcurrentPreviewSceneName: string;\n\t\t/**\n\t\t * Current preview scene UUID. `null` if not in studio mode\n\t\t */\n\t\tcurrentPreviewSceneUuid: string;\n\t\t/**\n\t\t * Array of scenes\n\t\t */\n\t\tscenes: JsonObject[];\n\t};\n\tGetGroupList: {\n\t\t/**\n\t\t * Array of group names\n\t\t */\n\t\tgroups: string[];\n\t};\n\tGetCurrentProgramScene: {\n\t\t/**\n\t\t * Current program scene name\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * Current program scene UUID\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Current program scene name (Deprecated)\n\t\t */\n\t\tcurrentProgramSceneName: string;\n\t\t/**\n\t\t * Current program scene UUID (Deprecated)\n\t\t */\n\t\tcurrentProgramSceneUuid: string;\n\t};\n\tSetCurrentProgramScene: undefined;\n\tGetCurrentPreviewScene: {\n\t\t/**\n\t\t * Current preview scene name\n\t\t */\n\t\tsceneName: string;\n\t\t/**\n\t\t * Current preview scene UUID\n\t\t */\n\t\tsceneUuid: string;\n\t\t/**\n\t\t * Current preview scene name\n\t\t */\n\t\tcurrentPreviewSceneName: string;\n\t\t/**\n\t\t * Current preview scene UUID\n\t\t */\n\t\tcurrentPreviewSceneUuid: string;\n\t};\n\tSetCurrentPreviewScene: undefined;\n\tCreateScene: {\n\t\t/**\n\t\t * UUID of the created scene\n\t\t */\n\t\tsceneUuid: string;\n\t};\n\tRemoveScene: undefined;\n\tSetSceneName: undefined;\n\tGetSceneSceneTransitionOverride: {\n\t\t/**\n\t\t * Name of the overridden scene transition, else `null`\n\t\t */\n\t\ttransitionName: string;\n\t\t/**\n\t\t * Duration of the overridden scene transition, else `null`\n\t\t */\n\t\ttransitionDuration: number;\n\t};\n\tSetSceneSceneTransitionOverride: undefined;\n\tGetSourceActive: {\n\t\t/**\n\t\t * Whether the source is showing in Program\n\t\t */\n\t\tvideoActive: boolean;\n\t\t/**\n\t\t * Whether the source is showing in the UI (Preview, Projector, Properties)\n\t\t */\n\t\tvideoShowing: boolean;\n\t};\n\tGetSourceScreenshot: {\n\t\t/**\n\t\t * Base64-encoded screenshot\n\t\t */\n\t\timageData: string;\n\t};\n\tSaveSourceScreenshot: undefined;\n\tGetStreamStatus: {\n\t\t/**\n\t\t * Whether the output is active\n\t\t */\n\t\toutputActive: boolean;\n\t\t/**\n\t\t * Whether the output is currently reconnecting\n\t\t */\n\t\toutputReconnecting: boolean;\n\t\t/**\n\t\t * Current formatted timecode string for the output\n\t\t */\n\t\toutputTimecode: string;\n\t\t/**\n\t\t * Current duration in milliseconds for the output\n\t\t */\n\t\toutputDuration: number;\n\t\t/**\n\t\t * Congestion of the output\n\t\t */\n\t\toutputCongestion: number;\n\t\t/**\n\t\t * Number of bytes sent by the output\n\t\t */\n\t\toutputBytes: number;\n\t\t/**\n\t\t * Number of frames skipped by the output's process\n\t\t */\n\t\toutputSkippedFrames: number;\n\t\t/**\n\t\t * Total number of frames delivered by the output's process\n\t\t */\n\t\toutputTotalFrames: number;\n\t};\n\tToggleStream: {\n\t\t/**\n\t\t * New state of the stream output\n\t\t */\n\t\toutputActive: boolean;\n\t};\n\tStartStream: undefined;\n\tStopStream: undefined;\n\tSendStreamCaption: undefined;\n\tGetTransitionKindList: {\n\t\t/**\n\t\t * Array of transition kinds\n\t\t */\n\t\ttransitionKinds: string[];\n\t};\n\tGetSceneTransitionList: {\n\t\t/**\n\t\t * Name of the current scene transition. Can be null\n\t\t */\n\t\tcurrentSceneTransitionName: string;\n\t\t/**\n\t\t * UUID of the current scene transition. Can be null\n\t\t */\n\t\tcurrentSceneTransitionUuid: string;\n\t\t/**\n\t\t * Kind of the current scene transition. Can be null\n\t\t */\n\t\tcurrentSceneTransitionKind: string;\n\t\t/**\n\t\t * Array of transitions\n\t\t */\n\t\ttransitions: JsonObject[];\n\t};\n\tGetCurrentSceneTransition: {\n\t\t/**\n\t\t * Name of the transition\n\t\t */\n\t\ttransitionName: string;\n\t\t/**\n\t\t * UUID of the transition\n\t\t */\n\t\ttransitionUuid: string;\n\t\t/**\n\t\t * Kind of the transition\n\t\t */\n\t\ttransitionKind: string;\n\t\t/**\n\t\t * Whether the transition uses a fixed (unconfigurable) duration\n\t\t */\n\t\ttransitionFixed: boolean;\n\t\t/**\n\t\t * Configured transition duration in milliseconds. `null` if transition is fixed\n\t\t */\n\t\ttransitionDuration: number;\n\t\t/**\n\t\t * Whether the transition supports being configured\n\t\t */\n\t\ttransitionConfigurable: boolean;\n\t\t/**\n\t\t * Object of settings for the transition. `null` if transition is not configurable\n\t\t */\n\t\ttransitionSettings: JsonObject;\n\t};\n\tSetCurrentSceneTransition: undefined;\n\tSetCurrentSceneTransitionDuration: undefined;\n\tSetCurrentSceneTransitionSettings: undefined;\n\tGetCurrentSceneTransitionCursor: {\n\t\t/**\n\t\t * Cursor position, between 0.0 and 1.0\n\t\t */\n\t\ttransitionCursor: number;\n\t};\n\tTriggerStudioModeTransition: undefined;\n\tSetTBarPosition: undefined;\n\tGetStudioModeEnabled: {\n\t\t/**\n\t\t * Whether studio mode is enabled\n\t\t */\n\t\tstudioModeEnabled: boolean;\n\t};\n\tSetStudioModeEnabled: undefined;\n\tOpenInputPropertiesDialog: undefined;\n\tOpenInputFiltersDialog: undefined;\n\tOpenInputInteractDialog: undefined;\n\tGetMonitorList: {\n\t\t/**\n\t\t * a list of detected monitors with some information\n\t\t */\n\t\tmonitors: JsonObject[];\n\t};\n\tOpenVideoMixProjector: undefined;\n\tOpenSourceProjector: undefined;\n}\n", "import createDebug from 'debug';\nimport {EventEmitter} from 'eventemitter3';\n// Import under alias so DOM's WebSocket type can be used\nimport WebSocketIpml from 'isomorphic-ws';\nimport type {Except, Merge, SetOptional} from 'type-fest';\n\nimport {WebSocketOpCode} from './types.js';\nimport type {OutgoingMessageTypes, OutgoingMessage, OBSEventTypes, IncomingMessage, IncomingMessageTypes, OBSRequestTypes, OBSResponseTypes, RequestMessage, RequestBatchExecutionType, RequestBatchRequest, RequestBatchMessage, ResponseMessage, ResponseBatchMessage, RequestBatchOptions} from './types.js';\nimport authenticationHashing from './utils/authenticationHashing.js';\n\nconst debug = createDebug('obs-websocket-js');\n\nexport class OBSWebSocketError extends Error {\n\tconstructor(public code: number, message: string) {\n\t\tsuper(message);\n\t}\n}\n\nexport type EventTypes = Merge<{\n\tConnectionOpened: void;\n\tConnectionClosed: OBSWebSocketError;\n\tConnectionError: OBSWebSocketError;\n\tHello: IncomingMessageTypes[WebSocketOpCode.Hello];\n\tIdentified: IncomingMessageTypes[WebSocketOpCode.Identified];\n}, OBSEventTypes>;\n\n// EventEmitter expects {type: [value]} syntax while for us {type: value} is neater\ntype MapValueToArgsArray<T extends Record<string, unknown>> = {\n\t// eslint-disable-next-line @typescript-eslint/ban-types\n\t[K in keyof T]: T[K] extends void ? [] : [T[K]];\n};\n\ntype IdentificationInput = SetOptional<Except<OutgoingMessageTypes[WebSocketOpCode.Identify], 'authentication'>, 'rpcVersion'>;\ntype HelloIdentifiedMerged = Merge<\nExclude<IncomingMessageTypes[WebSocketOpCode.Hello], 'authenticate'>,\nIncomingMessageTypes[WebSocketOpCode.Identified]\n>;\n\nexport abstract class BaseOBSWebSocket extends EventEmitter<MapValueToArgsArray<EventTypes>> {\n\tprotected static requestCounter = 1;\n\n\tprotected static generateMessageId(): string {\n\t\treturn String(BaseOBSWebSocket.requestCounter++);\n\t}\n\n\tprotected _identified = false;\n\tprotected internalListeners = new EventEmitter();\n\tprotected socket?: WebSocket;\n\tprotected abstract protocol: string;\n\n\tpublic get identified() {\n\t\treturn this._identified;\n\t}\n\n\t/**\n\t * Connect to an obs-websocket server\n\t * @param url Websocket server to connect to (including ws:// or wss:// protocol)\n\t * @param password Password\n\t * @param identificationParams Data for Identify event\n\t * @returns Hello & Identified messages data (combined)\n\t */\n\tasync connect(\n\t\turl = 'ws://127.0.0.1:4455',\n\t\tpassword?: string,\n\t\tidentificationParams: IdentificationInput = {},\n\t): Promise<HelloIdentifiedMerged> {\n\t\tif (this.socket) {\n\t\t\tawait this.disconnect();\n\t\t}\n\n\t\ttry {\n\t\t\tconst connectionClosedPromise = this.internalEventPromise<EventTypes['ConnectionClosed']>('ConnectionClosed');\n\t\t\tconst connectionErrorPromise = this.internalEventPromise<EventTypes['ConnectionError']>('ConnectionError');\n\n\t\t\treturn await Promise.race([\n\t\t\t\t(async () => {\n\t\t\t\t\tconst hello = await this.createConnection(url);\n\t\t\t\t\tthis.emit('Hello', hello);\n\t\t\t\t\treturn this.identify(hello, password, identificationParams);\n\t\t\t\t})(),\n\t\t\t\t// Choose the best promise for connection error/close\n\t\t\t\t// In browser connection close has close code + reason,\n\t\t\t\t// while in node error event has these\n\t\t\t\tnew Promise<never>((resolve, reject) => {\n\t\t\t\t\tvoid connectionErrorPromise.then(e => {\n\t\t\t\t\t\tif (e.message) {\n\t\t\t\t\t\t\treject(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tvoid connectionClosedPromise.then(e => {\n\t\t\t\t\t\treject(e);\n\t\t\t\t\t});\n\t\t\t\t}),\n\t\t\t]);\n\t\t} catch (error: unknown) {\n\t\t\tawait this.disconnect();\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\t/**\n\t * Disconnect from obs-websocket server\n\t */\n\tasync disconnect() {\n\t\tif (!this.socket || this.socket.readyState === WebSocketIpml.CLOSED) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst connectionClosedPromise = this.internalEventPromise('ConnectionClosed');\n\t\tthis.socket.close();\n\t\tawait connectionClosedPromise;\n\t}\n\n\t/**\n\t * Update session parameters\n\t * @param data Reidentify data\n\t * @returns Identified message data\n\t */\n\tasync reidentify(data: OutgoingMessageTypes[WebSocketOpCode.Reidentify]) {\n\t\tconst identifiedPromise = this.internalEventPromise<IncomingMessageTypes[WebSocketOpCode.Identified]>(`op:${WebSocketOpCode.Identified}`);\n\t\tawait this.message(WebSocketOpCode.Reidentify, data);\n\t\treturn identifiedPromise;\n\t}\n\n\t/**\n\t * Send a request to obs-websocket\n\t * @param requestType Request name\n\t * @param requestData Request data\n\t * @returns Request response\n\t */\n\tasync call<Type extends keyof OBSRequestTypes>(requestType: Type, requestData?: OBSRequestTypes[Type]): Promise<OBSResponseTypes[Type]> {\n\t\tconst requestId = BaseOBSWebSocket.generateMessageId();\n\t\tconst responsePromise = this.internalEventPromise<ResponseMessage<Type>>(`res:${requestId}`);\n\t\tawait this.message(WebSocketOpCode.Request, {\n\t\t\trequestId,\n\t\t\trequestType,\n\t\t\trequestData,\n\t\t} as RequestMessage<Type>);\n\t\tconst {requestStatus, responseData} = await responsePromise;\n\n\t\tif (!requestStatus.result) {\n\t\t\tthrow new OBSWebSocketError(requestStatus.code, requestStatus.comment);\n\t\t}\n\n\t\treturn responseData as OBSResponseTypes[Type];\n\t}\n\n\t/**\n\t * Send a batch request to obs-websocket\n\t * @param requests Array of Request objects (type and data)\n\t * @param options A set of options for how the batch will be executed\n\t * @param options.executionType The mode of execution obs-websocket will run the batch in\n\t * @param options.haltOnFailure Whether obs-websocket should stop executing the batch if one request fails\n\t * @returns RequestBatch response\n\t */\n\tasync callBatch(requests: RequestBatchRequest[], options: RequestBatchOptions = {}): Promise<ResponseMessage[]> {\n\t\tconst requestId = BaseOBSWebSocket.generateMessageId();\n\t\tconst responsePromise = this.internalEventPromise<ResponseBatchMessage>(`res:${requestId}`);\n\n\t\tawait this.message(WebSocketOpCode.RequestBatch, {\n\t\t\trequestId,\n\t\t\trequests,\n\t\t\t...options,\n\t\t});\n\n\t\tconst {results} = await responsePromise;\n\t\treturn results;\n\t}\n\n\t/**\n\t * Cleanup from socket disconnection\n\t */\n\tprotected cleanup() {\n\t\tif (!this.socket) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.socket.onopen = null;\n\t\tthis.socket.onmessage = null;\n\t\tthis.socket.onerror = null;\n\t\tthis.socket.onclose = null;\n\t\tthis.socket = undefined;\n\t\tthis._identified = false;\n\n\t\t// Cleanup leftovers\n\t\tthis.internalListeners.removeAllListeners();\n\t}\n\n\t/**\n\t * Create connection to specified obs-websocket server\n\t *\n\t * @private\n\t * @param url Websocket address\n\t * @returns Promise for hello data\n\t */\n\tprotected async createConnection(url: string) {\n\t\tconst connectionOpenedPromise = this.internalEventPromise('ConnectionOpened');\n\t\tconst helloPromise = this.internalEventPromise<IncomingMessageTypes[WebSocketOpCode.Hello]>(`op:${WebSocketOpCode.Hello}`);\n\n\t\tthis.socket = new WebSocketIpml(url, this.protocol) as unknown as WebSocket;\n\t\tthis.socket.onopen = this.onOpen.bind(this);\n\t\tthis.socket.onmessage = this.onMessage.bind(this);\n\t\tthis.socket.onerror = this.onError.bind(this) as (e: Event) => void;\n\t\tthis.socket.onclose = this.onClose.bind(this);\n\n\t\tawait connectionOpenedPromise;\n\t\tconst protocol = this.socket?.protocol;\n\t\t// Browsers don't autoclose on missing/wrong protocol\n\t\tif (!protocol) {\n\t\t\tthrow new OBSWebSocketError(-1, 'Server sent no subprotocol');\n\t\t}\n\n\t\tif (protocol !== this.protocol) {\n\t\t\tthrow new OBSWebSocketError(-1, 'Server sent an invalid subprotocol');\n\t\t}\n\n\t\treturn helloPromise;\n\t}\n\n\t/**\n\t * Send identify message\n\t *\n\t * @private\n\t * @param hello Hello message data\n\t * @param password Password\n\t * @param identificationParams Identification params\n\t * @returns Hello & Identified messages data (combined)\n\t */\n\tprotected async identify(\n\t\t{\n\t\t\tauthentication,\n\t\t\trpcVersion,\n\t\t\t...helloRest\n\t\t}: IncomingMessageTypes[WebSocketOpCode.Hello],\n\t\tpassword?: string,\n\t\tidentificationParams: IdentificationInput = {},\n\t): Promise<HelloIdentifiedMerged> {\n\t\t// Set rpcVersion if unset\n\t\tconst data: OutgoingMessageTypes[WebSocketOpCode.Identify] = {\n\t\t\trpcVersion,\n\t\t\t...identificationParams,\n\t\t};\n\n\t\tif (authentication && password) {\n\t\t\tdata.authentication = authenticationHashing(authentication.salt, authentication.challenge, password);\n\t\t}\n\n\t\tconst identifiedPromise = this.internalEventPromise<IncomingMessageTypes[WebSocketOpCode.Identified]>(`op:${WebSocketOpCode.Identified}`);\n\t\tawait this.message(WebSocketOpCode.Identify, data);\n\t\tconst identified = await identifiedPromise;\n\t\tthis._identified = true;\n\t\tthis.emit('Identified', identified);\n\n\t\treturn {\n\t\t\trpcVersion,\n\t\t\t...helloRest,\n\t\t\t...identified,\n\t\t};\n\t}\n\n\t/**\n\t * Send message to obs-websocket\n\t *\n\t * @private\n\t * @param op WebSocketOpCode\n\t * @param d Message data\n\t */\n\tprotected async message<Type extends keyof OutgoingMessageTypes>(op: Type, d: OutgoingMessageTypes[Type]) {\n\t\tif (!this.socket) {\n\t\t\tthrow new Error('Not connected');\n\t\t}\n\n\t\tif (!this.identified && op !== 1) {\n\t\t\tthrow new Error('Socket not identified');\n\t\t}\n\n\t\tconst encoded = await this.encodeMessage({\n\t\t\top,\n\t\t\td,\n\t\t} as OutgoingMessage);\n\t\tthis.socket.send(encoded);\n\t}\n\n\t/**\n\t * Create a promise to listen for an event on internal listener\n\t * (will be cleaned up on disconnect)\n\t *\n\t * @private\n\t * @param event Event to listen to\n\t * @returns Event data\n\t */\n\tprotected async internalEventPromise<ReturnVal = unknown>(event: string): Promise<ReturnVal> {\n\t\treturn new Promise(resolve => {\n\t\t\tthis.internalListeners.once(event, resolve);\n\t\t});\n\t}\n\n\t/**\n\t * Websocket open event listener\n\t *\n\t * @private\n\t * @param e Event\n\t */\n\tprotected onOpen(e: Event) {\n\t\tdebug('socket.open');\n\t\tthis.emit('ConnectionOpened');\n\t\tthis.internalListeners.emit('ConnectionOpened', e);\n\t}\n\n\t/**\n\t * Websocket message event listener\n\t *\n\t * @private\n\t * @param e Event\n\t */\n\tprotected async onMessage(e: MessageEvent<string | Blob | ArrayBuffer>) {\n\t\ttry {\n\t\t\tconst {op, d} = await this.decodeMessage(e.data);\n\t\t\tdebug('socket.message: %d %j', op, d);\n\n\t\t\tif (op === undefined || d === undefined) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tswitch (op) {\n\t\t\t\tcase WebSocketOpCode.Event: {\n\t\t\t\t\tconst {eventType, eventData} = d;\n\t\t\t\t\t// @ts-expect-error Typescript just doesn't understand it\n\t\t\t\t\tthis.emit(eventType, eventData);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tcase WebSocketOpCode.RequestResponse:\n\t\t\t\tcase WebSocketOpCode.RequestBatchResponse: {\n\t\t\t\t\tconst {requestId} = d;\n\t\t\t\t\tthis.internalListeners.emit(`res:${requestId}`, d);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tdefault:\n\t\t\t\t\tthis.internalListeners.emit(`op:${op}`, d);\n\t\t\t}\n\t\t} catch (error: unknown) {\n\t\t\tdebug('error handling message: %o', error);\n\t\t}\n\t}\n\n\t/**\n\t * Websocket error event listener\n\t *\n\t * @private\n\t * @param e ErrorEvent\n\t */\n\tprotected onError(e: ErrorEvent) {\n\t\tdebug('socket.error: %o', e);\n\t\tconst error = new OBSWebSocketError(-1, e.message);\n\n\t\tthis.emit('ConnectionError', error);\n\t\tthis.internalListeners.emit('ConnectionError', error);\n\t}\n\n\t/**\n\t * Websocket close event listener\n\t *\n\t * @private\n\t * @param e Event\n\t */\n\tprotected onClose(e: CloseEvent) {\n\t\tdebug('socket.close: %s (%d)', e.reason, e.code);\n\t\tconst error = new OBSWebSocketError(e.code, e.reason);\n\n\t\tthis.emit('ConnectionClosed', error);\n\t\tthis.internalListeners.emit('ConnectionClosed', error);\n\t\tthis.cleanup();\n\t}\n\n\t/**\n\t * Encode a message for specified protocol\n\t * @param data Outgoing message\n\t * @returns Outgoing message to send via websocket\n\t */\n\tprotected abstract encodeMessage(data: OutgoingMessage): Promise<string | Blob | ArrayBufferView>;\n\n\t/**\n\t * Decode a message for specified protocol\n\t * @param data Incoming message from websocket\n\t * @returns Parsed incoming message\n\t */\n\tprotected abstract decodeMessage(data: string | ArrayBuffer | Blob): Promise<IncomingMessage>;\n}\n\n// https://github.com/developit/microbundle/issues/531#issuecomment-575473024\n// Not using ESM export due to it also being detected and breaking rollup based bundlers (vite)\nif (typeof exports !== 'undefined') {\n\tObject.defineProperty(exports, '__esModule', {value: true});\n}\n", "import sha256 from 'crypto-js/sha256.js';\nimport Base64 from 'crypto-js/enc-base64.js';\n\n/**\n * SHA256 Hashing.\n * @param  {string} [salt=''] salt.\n * @param  {string} [challenge=''] challenge.\n * @param  {string} msg Message to encode.\n * @returns {string} sha256 encoded string.\n */\nexport default function (salt: string, challenge: string, msg: string): string {\n\tconst hash = Base64.stringify(sha256(msg + salt))!;\n\n\treturn Base64.stringify(sha256(hash + challenge))!;\n}\n"], "mappings": ";AAMO,IAAK,kBAAL,kBAAKA,qBAAL;AAMN,EAAAA,kCAAA,WAAQ,KAAR;AAMA,EAAAA,kCAAA,cAAW,KAAX;AAMA,EAAAA,kCAAA,gBAAa,KAAb;AAMA,EAAAA,kCAAA,gBAAa,KAAb;AAMA,EAAAA,kCAAA,WAAQ,KAAR;AAMA,EAAAA,kCAAA,aAAU,KAAV;AAMA,EAAAA,kCAAA,qBAAkB,KAAlB;AAMA,EAAAA,kCAAA,kBAAe,KAAf;AAMA,EAAAA,kCAAA,0BAAuB,KAAvB;AAtDW,SAAAA;AAAA,GAAA;AA0DL,IAAK,oBAAL,kBAAKC,uBAAL;AAMN,EAAAA,sCAAA,UAAO,KAAP;AAMA,EAAAA,sCAAA,aAAW,KAAX;AAMA,EAAAA,sCAAA,YAAU,KAAV;AAMA,EAAAA,sCAAA,YAAU,KAAV;AAMA,EAAAA,sCAAA,YAAU,KAAV;AAMA,EAAAA,sCAAA,iBAAe,MAAf;AAMA,EAAAA,sCAAA,aAAW,MAAX;AAMA,EAAAA,sCAAA,aAAW,MAAX;AAMA,EAAAA,sCAAA,gBAAc,OAAd;AAMA,EAAAA,sCAAA,iBAAe,OAAf;AAMA,EAAAA,sCAAA,aAAW,OAAX;AAMA,EAAAA,sCAAA,QAAM,QAAN;AAMA,EAAAA,sCAAA,SAAO,QAAP;AAMA,EAAAA,sCAAA,uBAAqB,SAArB;AAMA,EAAAA,sCAAA,6BAA2B,UAA3B;AAMA,EAAAA,sCAAA,2BAAyB,UAAzB;AAMA,EAAAA,sCAAA,+BAA6B,UAA7B;AAtGW,SAAAA;AAAA,GAAA;AA0GL,IAAK,4BAAL,kBAAKC,+BAAL;AAMN,EAAAA,sDAAA,UAAO,MAAP;AAQA,EAAAA,sDAAA,oBAAiB,KAAjB;AAQA,EAAAA,sDAAA,iBAAc,KAAd;AASA,EAAAA,sDAAA,cAAW,KAAX;AA/BW,SAAAA;AAAA,GAAA;;;AC1KZ,OAAO,iBAAiB;AACxB,SAAQ,oBAAmB;AAE3B,OAAO,mBAAmB;;;ACH1B,OAAO,YAAY;AACnB,OAAO,YAAY;AASJ,SAAR,8BAAkB,MAAc,WAAmB,KAAqB;AAC9E,QAAM,OAAO,OAAO,UAAU,OAAO,MAAM,IAAI,CAAC;AAEhD,SAAO,OAAO,UAAU,OAAO,OAAO,SAAS,CAAC;AACjD;;;ADJA,IAAM,QAAQ,YAAY,kBAAkB;AAErC,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAC5C,YAAmB,MAAc,SAAiB;AACjD,UAAM,OAAO;AADK;AAAA,EAEnB;AACD;AAsBO,IAAe,mBAAf,MAAe,0BAAyB,aAA8C;AAAA,EAC5F,OAAiB,iBAAiB;AAAA,EAElC,OAAiB,oBAA4B;AAC5C,WAAO,OAAO,kBAAiB,gBAAgB;AAAA,EAChD;AAAA,EAEU,cAAc;AAAA,EACd,oBAAoB,IAAI,aAAa;AAAA,EACrC;AAAA,EAGV,IAAW,aAAa;AACvB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,QACL,MAAM,uBACN,UACA,uBAA4C,CAAC,GACZ;AACjC,QAAI,KAAK,QAAQ;AAChB,YAAM,KAAK,WAAW;AAAA,IACvB;AAEA,QAAI;AACH,YAAM,0BAA0B,KAAK,qBAAqD,kBAAkB;AAC5G,YAAM,yBAAyB,KAAK,qBAAoD,iBAAiB;AAEzG,aAAO,MAAM,QAAQ,KAAK;AAAA,SACxB,YAAY;AACZ,gBAAM,QAAQ,MAAM,KAAK,iBAAiB,GAAG;AAC7C,eAAK,KAAK,SAAS,KAAK;AACxB,iBAAO,KAAK,SAAS,OAAO,UAAU,oBAAoB;AAAA,QAC3D,GAAG;AAAA;AAAA;AAAA;AAAA,QAIH,IAAI,QAAe,CAAC,SAAS,WAAW;AACvC,eAAK,uBAAuB,KAAK,OAAK;AACrC,gBAAI,EAAE,SAAS;AACd,qBAAO,CAAC;AAAA,YACT;AAAA,UACD,CAAC;AACD,eAAK,wBAAwB,KAAK,OAAK;AACtC,mBAAO,CAAC;AAAA,UACT,CAAC;AAAA,QACF,CAAC;AAAA,MACF,CAAC;AAAA,IACF,SAAS,OAAgB;AACxB,YAAM,KAAK,WAAW;AACtB,YAAM;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa;AAClB,QAAI,CAAC,KAAK,UAAU,KAAK,OAAO,eAAe,cAAc,QAAQ;AACpE;AAAA,IACD;AAEA,UAAM,0BAA0B,KAAK,qBAAqB,kBAAkB;AAC5E,SAAK,OAAO,MAAM;AAClB,UAAM;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,MAAwD;AACxE,UAAM,oBAAoB,KAAK,qBAAuE,wBAAgC,EAAE;AACxI,UAAM,KAAK,4BAAoC,IAAI;AACnD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAyC,aAAmB,aAAsE;AACvI,UAAM,YAAY,kBAAiB,kBAAkB;AACrD,UAAM,kBAAkB,KAAK,qBAA4C,OAAO,SAAS,EAAE;AAC3F,UAAM,KAAK,yBAAiC;AAAA,MAC3C;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAyB;AACzB,UAAM,EAAC,eAAe,aAAY,IAAI,MAAM;AAE5C,QAAI,CAAC,cAAc,QAAQ;AAC1B,YAAM,IAAI,kBAAkB,cAAc,MAAM,cAAc,OAAO;AAAA,IACtE;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,UAAU,UAAiC,UAA+B,CAAC,GAA+B;AAC/G,UAAM,YAAY,kBAAiB,kBAAkB;AACrD,UAAM,kBAAkB,KAAK,qBAA2C,OAAO,SAAS,EAAE;AAE1F,UAAM,KAAK,8BAAsC;AAAA,MAChD;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACJ,CAAC;AAED,UAAM,EAAC,QAAO,IAAI,MAAM;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKU,UAAU;AACnB,QAAI,CAAC,KAAK,QAAQ;AACjB;AAAA,IACD;AAEA,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,UAAU;AACtB,SAAK,OAAO,UAAU;AACtB,SAAK,SAAS;AACd,SAAK,cAAc;AAGnB,SAAK,kBAAkB,mBAAmB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAgB,iBAAiB,KAAa;AAnM/C;AAoME,UAAM,0BAA0B,KAAK,qBAAqB,kBAAkB;AAC5E,UAAM,eAAe,KAAK,qBAAkE,mBAA2B,EAAE;AAEzH,SAAK,SAAS,IAAI,cAAc,KAAK,KAAK,QAAQ;AAClD,SAAK,OAAO,SAAS,KAAK,OAAO,KAAK,IAAI;AAC1C,SAAK,OAAO,YAAY,KAAK,UAAU,KAAK,IAAI;AAChD,SAAK,OAAO,UAAU,KAAK,QAAQ,KAAK,IAAI;AAC5C,SAAK,OAAO,UAAU,KAAK,QAAQ,KAAK,IAAI;AAE5C,UAAM;AACN,UAAM,YAAW,UAAK,WAAL,mBAAa;AAE9B,QAAI,CAAC,UAAU;AACd,YAAM,IAAI,kBAAkB,IAAI,4BAA4B;AAAA,IAC7D;AAEA,QAAI,aAAa,KAAK,UAAU;AAC/B,YAAM,IAAI,kBAAkB,IAAI,oCAAoC;AAAA,IACrE;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAgB,SACf;AAAA,IACC;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GACA,UACA,uBAA4C,CAAC,GACZ;AAEjC,UAAM,OAAuD;AAAA,MAC5D;AAAA,MACA,GAAG;AAAA,IACJ;AAEA,QAAI,kBAAkB,UAAU;AAC/B,WAAK,iBAAiB,8BAAsB,eAAe,MAAM,eAAe,WAAW,QAAQ;AAAA,IACpG;AAEA,UAAM,oBAAoB,KAAK,qBAAuE,wBAAgC,EAAE;AACxI,UAAM,KAAK,0BAAkC,IAAI;AACjD,UAAM,aAAa,MAAM;AACzB,SAAK,cAAc;AACnB,SAAK,KAAK,cAAc,UAAU;AAElC,WAAO;AAAA,MACN;AAAA,MACA,GAAG;AAAA,MACH,GAAG;AAAA,IACJ;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAgB,QAAiD,IAAU,GAA+B;AACzG,QAAI,CAAC,KAAK,QAAQ;AACjB,YAAM,IAAI,MAAM,eAAe;AAAA,IAChC;AAEA,QAAI,CAAC,KAAK,cAAc,OAAO,GAAG;AACjC,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AAEA,UAAM,UAAU,MAAM,KAAK,cAAc;AAAA,MACxC;AAAA,MACA;AAAA,IACD,CAAoB;AACpB,SAAK,OAAO,KAAK,OAAO;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAgB,qBAA0C,OAAmC;AAC5F,WAAO,IAAI,QAAQ,aAAW;AAC7B,WAAK,kBAAkB,KAAK,OAAO,OAAO;AAAA,IAC3C,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQU,OAAO,GAAU;AAC1B,UAAM,aAAa;AACnB,SAAK,KAAK,kBAAkB;AAC5B,SAAK,kBAAkB,KAAK,oBAAoB,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAgB,UAAU,GAA8C;AACvE,QAAI;AACH,YAAM,EAAC,IAAI,EAAC,IAAI,MAAM,KAAK,cAAc,EAAE,IAAI;AAC/C,YAAM,yBAAyB,IAAI,CAAC;AAEpC,UAAI,OAAO,UAAa,MAAM,QAAW;AACxC;AAAA,MACD;AAEA,cAAQ,IAAI;AAAA,QACX,oBAA4B;AAC3B,gBAAM,EAAC,WAAW,UAAS,IAAI;AAE/B,eAAK,KAAK,WAAW,SAAS;AAC9B;AAAA,QACD;AAAA,QAEA;AAAA,QACA,mCAA2C;AAC1C,gBAAM,EAAC,UAAS,IAAI;AACpB,eAAK,kBAAkB,KAAK,OAAO,SAAS,IAAI,CAAC;AACjD;AAAA,QACD;AAAA,QAEA;AACC,eAAK,kBAAkB,KAAK,MAAM,EAAE,IAAI,CAAC;AAAA,MAC3C;AAAA,IACD,SAAS,OAAgB;AACxB,YAAM,8BAA8B,KAAK;AAAA,IAC1C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQU,QAAQ,GAAe;AAChC,UAAM,oBAAoB,CAAC;AAC3B,UAAM,QAAQ,IAAI,kBAAkB,IAAI,EAAE,OAAO;AAEjD,SAAK,KAAK,mBAAmB,KAAK;AAClC,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQU,QAAQ,GAAe;AAChC,UAAM,yBAAyB,EAAE,QAAQ,EAAE,IAAI;AAC/C,UAAM,QAAQ,IAAI,kBAAkB,EAAE,MAAM,EAAE,MAAM;AAEpD,SAAK,KAAK,oBAAoB,KAAK;AACnC,SAAK,kBAAkB,KAAK,oBAAoB,KAAK;AACrD,SAAK,QAAQ;AAAA,EACd;AAeD;AAIA,IAAI,OAAO,YAAY,aAAa;AACnC,SAAO,eAAe,SAAS,cAAc,EAAC,OAAO,KAAI,CAAC;AAC3D;", "names": ["WebSocketOpCode", "EventSubscription", "RequestBatchExecutionType"]}