{"version": 3, "sources": ["../src/msgpack.ts"], "sourcesContent": ["import {decode, encode} from '@msgpack/msgpack';\nimport {BaseOBSWebSocket} from './base.js';\nexport {OBSWebSocketError} from './base.js';\nexport type {EventTypes} from './base.js';\nimport type {IncomingMessage, OutgoingMessage} from './types.js';\nexport * from './types.js';\n\nexport class OBSWebSocket extends BaseOBSWebSocket {\n\tprotocol = 'obswebsocket.msgpack';\n\n\tprotected async encodeMessage(data: OutgoingMessage): Promise<ArrayBufferView> {\n\t\treturn encode(data);\n\t}\n\n\tprotected async decodeMessage(data: ArrayBuffer | Blob): Promise<IncomingMessage> {\n\t\t// Browsers provide Blob while node gives straight ArrayBuffer\n\t\tif (typeof Blob !== 'undefined' && data instanceof Blob) {\n\t\t\tdata = await data.arrayBuffer();\n\t\t}\n\n\t\treturn decode(data as ArrayBuffer) as IncomingMessage;\n\t}\n}\n\nexport default OBSWebSocket;\n"], "mappings": ";;;;;;;;;AAAA,SAAQ,QAAQ,cAAa;AAOtB,IAAM,eAAN,cAA2B,iBAAiB;AAAA,EAClD,WAAW;AAAA,EAEX,MAAgB,cAAc,MAAiD;AAC9E,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA,EAEA,MAAgB,cAAc,MAAoD;AAEjF,QAAI,OAAO,SAAS,eAAe,gBAAgB,MAAM;AACxD,aAAO,MAAM,KAAK,YAAY;AAAA,IAC/B;AAEA,WAAO,OAAO,IAAmB;AAAA,EAClC;AACD;AAEA,IAAO,kBAAQ;", "names": []}