{"version": 3, "sources": ["../src/json.ts"], "sourcesContent": ["import {BaseOBSWebSocket} from './base.js';\nexport {OBSWebSocketError} from './base.js';\nexport type {EventTypes} from './base.js';\nimport {type IncomingMessage, type OutgoingMessage} from './types.js';\nexport * from './types.js';\n\nexport class OBSWebSocket extends BaseOBSWebSocket {\n\tprotocol = 'obswebsocket.json';\n\n\tprotected async encodeMessage(data: OutgoingMessage): Promise<string> {\n\t\treturn JSON.stringify(data);\n\t}\n\n\tprotected async decodeMessage(data: string): Promise<IncomingMessage> {\n\t\treturn JSON.parse(data) as IncomingMessage;\n\t}\n}\n\nexport default OBSWebSocket;\n"], "mappings": ";;;;;;;;;AAMO,IAAM,eAAN,cAA2B,iBAAiB;AAAA,EAClD,WAAW;AAAA,EAEX,MAAgB,cAAc,MAAwC;AACrE,WAAO,KAAK,UAAU,IAAI;AAAA,EAC3B;AAAA,EAEA,MAAgB,cAAc,MAAwC;AACrE,WAAO,KAAK,MAAM,IAAI;AAAA,EACvB;AACD;AAEA,IAAO,eAAQ;", "names": []}