import { ExtensionCodecType } from "./ExtensionCodec";
export declare const DEFAULT_MAX_DEPTH = 100;
export declare const DEFAULT_INITIAL_BUFFER_SIZE = 2048;
export declare class Encoder<ContextType = undefined> {
    private readonly extensionCodec;
    private readonly context;
    private readonly maxDepth;
    private readonly initialBufferSize;
    private readonly sortKeys;
    private readonly forceFloat32;
    private readonly ignoreUndefined;
    private readonly forceIntegerToFloat;
    private pos;
    private view;
    private bytes;
    constructor(extensionCodec?: ExtensionCodecType<ContextType>, context?: ContextType, maxDepth?: number, initialBufferSize?: number, sortKeys?: boolean, forceFloat32?: boolean, ignoreUndefined?: boolean, forceIntegerToFloat?: boolean);
    private reinitializeState;
    /**
     * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.
     *
     * @returns Encodes the object and returns a shared reference the encoder's internal buffer.
     */
    encodeSharedRef(object: unknown): Uint8Array;
    /**
     * @returns Encodes the object and returns a copy of the encoder's internal buffer.
     */
    encode(object: unknown): Uint8Array;
    private doEncode;
    private ensureBufferSizeToWrite;
    private resizeBuffer;
    private encodeNil;
    private encodeBoolean;
    private encodeNumber;
    private writeStringHeader;
    private encodeString;
    private encodeObject;
    private encodeBinary;
    private encodeArray;
    private countWithoutUndefined;
    private encodeMap;
    private encodeExtension;
    private writeU8;
    private writeU8a;
    private writeI8;
    private writeU16;
    private writeI16;
    private writeU32;
    private writeI32;
    private writeF32;
    private writeF64;
    private writeU64;
    private writeI64;
}
